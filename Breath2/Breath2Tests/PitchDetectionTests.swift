//
//  PitchDetectionTests.swift
//  Breath2Tests
//
//  Created by Mo on 02/06/2025.
//

import XCTest
@testable import Breath2

final class PitchDetectionTests: XCTestCase {

    var pitchDetector: PitchDetector!
    var techniqueAnalyzer: TechniqueAnalyzer!

    override func setUpWithError() throws {
        pitchDetector = PitchDetector(
            sampleRate: 44100.0,
            minFreq: 10,
            maxFreq: 40,
            freqAccuracy: 0.025,
            lowerFormantFreq: 250,
            decayRate: 0.8,
            minAmp: 2.0e-4
        )

        techniqueAnalyzer = TechniqueAnalyzer()
    }

    override func tearDownWithError() throws {
        pitchDetector = nil
        techniqueAnalyzer = nil
    }
    
    func testPitchDetectorInitialization() throws {
        XCTAssertNotNil(pitchDetector)
    }
    
    func testTechniqueAnalyzerInitialization() throws {
        XCTAssertNotNil(techniqueAnalyzer)
    }

    func testTechniqueClassification() throws {
        // Test zone classification based on frequency

        // Test low frequency (should give low effort)
        let zone15Hz = techniqueAnalyzer.classifyPitch(15.0)
        XCTAssertEqual(zone15Hz, .goodTechnique)

        // Test high frequency (should give high effort)
        let zone35Hz = techniqueAnalyzer.classifyPitch(35.0)
        XCTAssertEqual(zone35Hz, .highEffort)

        // Test medium frequency (should give good technique)
        let zone25Hz = techniqueAnalyzer.classifyPitch(25.0)
        XCTAssertEqual(zone25Hz, .goodTechnique)
    }
    
    func testFrequencyValidation() throws {
        // Test valid frequencies
        XCTAssertTrue(techniqueAnalyzer.isValidFrequency(15.0))
        XCTAssertTrue(techniqueAnalyzer.isValidFrequency(25.0))
        XCTAssertTrue(techniqueAnalyzer.isValidFrequency(35.0))

        // Test invalid frequencies
        XCTAssertFalse(techniqueAnalyzer.isValidFrequency(5.0))  // Too low
        XCTAssertFalse(techniqueAnalyzer.isValidFrequency(45.0)) // Too high
        XCTAssertFalse(techniqueAnalyzer.isValidFrequency(0.0))  // Zero
    }

    func testOptimalTechniqueValidation() throws {
        // Test technique in good zone
        XCTAssertTrue(techniqueAnalyzer.isOptimalTechnique(.goodTechnique))

        // Test technique outside good zone
        XCTAssertFalse(techniqueAnalyzer.isOptimalTechnique(.lowEffort))
        XCTAssertFalse(techniqueAnalyzer.isOptimalTechnique(.highEffort))
        XCTAssertFalse(techniqueAnalyzer.isOptimalTechnique(.noEffort))
    }

    // MARK: - New Paper Implementation Tests

    func testResultSavingConfiguration() throws {
        // Test with result saving enabled
        var config = AlgorithmConfiguration.standard
        config.saveResults = true

        let pitchDetectorWithSaving = PitchDetector(configuration: config)

        // Generate test audio data
        let sampleRate: Float = 44100.0
        let frequency: Float = 20.0 // 20 Hz test frequency
        let duration: Float = 0.2 // 200ms
        let sampleCount = Int(sampleRate * duration)

        var sineWave = [Float](repeating: 0.0, count: sampleCount)
        for i in 0..<sampleCount {
            let time = Float(i) / sampleRate
            sineWave[i] = sin(2.0 * Float.pi * frequency * time) * 0.1
        }

        // Process with proper sample indexing (Paper specification)
        let detectedPitch = pitchDetectorWithSaving.processChunk(sineWave, thisFirstNativeInd: 0, numElements: sampleCount)

        // Export results
        let (detections, correlations) = pitchDetectorWithSaving.exportResults()

        // Verify results were saved
        XCTAssertNotNil(detections, "Detection results should be available when saving is enabled")
        XCTAssertNotNil(correlations, "Correlation data should be available when saving is enabled")

        print("✅ Result saving test completed - Detected pitch: \(detectedPitch) Hz")
    }

    func testEconomicalAutocorrelationWithDerivative() throws {
        // Test the "one wavelength ahead" positioning and derivative-based refinement
        let config = AlgorithmConfiguration.standard
        let detector = PitchDetector(configuration: config)

        // Generate consistent test signal to establish pattern
        let sampleRate: Float = 44100.0
        let frequency: Float = 25.0 // 25 Hz test frequency
        let duration: Float = 0.1 // 100ms chunks
        let sampleCount = Int(sampleRate * duration)

        var sineWave = [Float](repeating: 0.0, count: sampleCount)
        for i in 0..<sampleCount {
            let time = Float(i) / sampleRate
            sineWave[i] = sin(2.0 * Float.pi * frequency * time) * 0.2
        }

        // Process multiple chunks to establish economical search pattern
        var totalSamples = 0
        var lastPitch: Float = 0.0

        for chunk in 0..<5 {
            let detectedPitch = detector.processChunk(sineWave, thisFirstNativeInd: totalSamples, numElements: sampleCount)
            totalSamples += sampleCount

            if detectedPitch > 0 {
                lastPitch = detectedPitch
                print("Chunk \(chunk): Detected \(detectedPitch) Hz at sample \(totalSamples)")
            }
        }

        // Verify economical search is working
        XCTAssertGreaterThan(lastPitch, 0, "Should detect pitch using economical autocorrelation")

        print("✅ Economical autocorrelation test completed - Final pitch: \(lastPitch) Hz")
    }

    func testPipelineStateManagement() throws {
        // Test proper incremental processing with sample indexing
        let config = AlgorithmConfiguration.standard
        let detector = PitchDetector(configuration: config)

        let sampleRate: Float = 44100.0
        let chunkSize = Int(sampleRate * 0.1) // 100ms chunks as per paper

        // Generate test signal
        var totalSamples = 0

        for chunk in 0..<3 {
            var audioChunk = [Float](repeating: 0.0, count: chunkSize)
            let frequency: Float = 20.0 + Float(chunk) * 2.0 // Varying frequency

            for i in 0..<chunkSize {
                let globalTime = Float(totalSamples + i) / sampleRate
                audioChunk[i] = sin(2.0 * Float.pi * frequency * globalTime) * 0.15
            }

            // Process with correct sample indexing (Paper specification)
            let detectedPitch = detector.processChunk(audioChunk, thisFirstNativeInd: totalSamples, numElements: chunkSize)
            totalSamples += chunkSize

            print("Chunk \(chunk): Processed samples \(totalSamples), detected \(detectedPitch) Hz")
        }

        XCTAssertEqual(totalSamples, chunkSize * 3, "Should track total samples correctly")

        print("✅ Pipeline state management test completed")
    }
    
    func testPressureFeedback() throws {
        let feedbackLow = pressureCalculator.getPressureFeedback(5.0)
        XCTAssertTrue(feedbackLow.contains("Too Low"))
        
        let feedbackBelowTarget = pressureCalculator.getPressureFeedback(8.0)
        XCTAssertTrue(feedbackBelowTarget.contains("Below Target"))
        
        let feedbackPerfect = pressureCalculator.getPressureFeedback(15.0)
        XCTAssertTrue(feedbackPerfect.contains("Perfect Range"))
        
        let feedbackAboveTarget = pressureCalculator.getPressureFeedback(22.0)
        XCTAssertTrue(feedbackAboveTarget.contains("Above Target"))
        
        let feedbackHigh = pressureCalculator.getPressureFeedback(28.0)
        XCTAssertTrue(feedbackHigh.contains("Too High"))
    }
    
    func testConfidenceLevel() throws {
        // Test confidence at center frequency (25 Hz)
        let confidenceCenter = pressureCalculator.getConfidenceLevel(25.0)
        XCTAssertEqual(confidenceCenter, 1.0, accuracy: 0.1)
        
        // Test confidence at edge frequencies
        let confidenceEdge1 = pressureCalculator.getConfidenceLevel(10.0)
        XCTAssertLessThan(confidenceEdge1, 1.0)
        
        let confidenceEdge2 = pressureCalculator.getConfidenceLevel(40.0)
        XCTAssertLessThan(confidenceEdge2, 1.0)
        
        // Test confidence for invalid frequency
        let confidenceInvalid = pressureCalculator.getConfidenceLevel(5.0)
        XCTAssertEqual(confidenceInvalid, 0.0)
    }
    
    func testSyntheticSineWave() throws {
        // Generate a synthetic sine wave at 20 Hz
        let frequency: Float = 20.0
        let sampleRate: Float = 44100.0
        let duration: Float = 0.5 // 0.5 seconds
        let amplitude: Float = 0.5
        
        let sampleCount = Int(sampleRate * duration)
        var sineWave = [Float](repeating: 0.0, count: sampleCount)
        
        for i in 0..<sampleCount {
            let t = Float(i) / sampleRate
            sineWave[i] = amplitude * sin(2.0 * Float.pi * frequency * t)
        }
        
        // Process the sine wave through our pitch detector
        let detectedPitch = pitchDetector.processChunk(sineWave)
        
        // The detected pitch should be close to 20 Hz
        // Note: Autocorrelation-based pitch detection may not be perfectly accurate for pure tones
        print("Generated frequency: \(frequency) Hz, Detected frequency: \(detectedPitch) Hz")
        
        // Allow for some tolerance in pitch detection
        if detectedPitch > 0 {
            XCTAssertEqual(detectedPitch, frequency, accuracy: 5.0, "Pitch detection should be within 5 Hz of the generated frequency")
        }
    }
    
    func testModelInfo() throws {
        let modelInfo = pressureCalculator.getModelInfo()
        
        XCTAssertNotNil(modelInfo["slope"])
        XCTAssertNotNil(modelInfo["intercept"])
        XCTAssertNotNil(modelInfo["r_squared"])
        XCTAssertNotNil(modelInfo["data_points"])
        
        // Verify the r² value from the paper
        if let rSquared = modelInfo["r_squared"] as? Double {
            XCTAssertEqual(rSquared, 0.886, accuracy: 0.001)
        }
        
        // Verify the data points count from the paper
        if let dataPoints = modelInfo["data_points"] as? Int {
            XCTAssertEqual(dataPoints, 9993)
        }
    }

    func testGaussianKernelProperties() throws {
        let config = AlgorithmConfiguration.standard

        // Calculate expected sigma
        let expectedDownsampledRate = config.sampleRate / Float(config.getDownsampleFactor())
        let expectedFwhmDs = 0.4 * (expectedDownsampledRate / Float(config.maxFreq))
        let expectedSigmaDs = expectedFwhmDs / 2.35482004503 // (2 * sqrt(2 * log(2)))

        // Assert that config.getGaussianSigma() is approximately equal to expectedSigmaDs
        XCTAssertEqual(config.getGaussianSigma(), expectedSigmaDs, accuracy: 0.0001, "Calculated sigma should match expected value")

        // Create the Gaussian kernel
        let actualSigma = config.getGaussianSigma()
        let kernelSize = Int(6 * actualSigma)
        let finalKernelSize = kernelSize % 2 == 0 ? kernelSize + 1 : kernelSize

        // If finalKernelSize <= 0, fail the test, as kernel must be positive
        XCTAssertGreaterThan(finalKernelSize, 0, "Kernel size must be positive")

        // Replicate the kernel generation logic from PitchDetector.createGaussianKernel()
        var kernel = [Float](repeating: 0.0, count: finalKernelSize)
        let center = Float(finalKernelSize - 1) / 2.0
        var sum: Float = 0.0
        for i in 0..<finalKernelSize {
            let x = Float(i) - center
            let value = exp(-(x * x) / (2.0 * actualSigma * actualSigma))
            kernel[i] = value
            sum += value
        }
        // Normalize kernel
        if sum != 0 { // Avoid division by zero if sum is zero
            for i in 0..<finalKernelSize {
                kernel[i] /= sum
            }
        } else {
            // Handle the case where sum is zero, perhaps by failing or setting kernel to a default
            // For this test, if sum is zero, the sum assertion later will fail, which is acceptable.
        }


        // Assert that the sum of elements in the kernel is approximately equal to 1.0
        let kernelSum = kernel.reduce(0, +)
        XCTAssertEqual(kernelSum, 1.0, accuracy: 0.0001, "Sum of kernel elements should be approximately 1.0")

        // Print the values for review
        print("Expected Sigma Ds: \(expectedSigmaDs)")
        print("Actual Sigma from config: \(actualSigma)")
        print("Final Kernel Size: \(finalKernelSize)")
        print("Sum of kernel elements: \(kernelSum)")
    }

    // Helper function to calculate RMS
    private func calculateRMS(data: [Float]) -> Float {
        guard !data.isEmpty else { return 0 }
        let sumOfSquares = data.map { $0 * $0 }.reduce(0, +)
        return sqrt(sumOfSquares / Float(data.count))
    }

    func testPreFilterFrequencyResponse() throws {
        var config = AlgorithmConfiguration.standard
        config.preFilterEnabled = true
        config.sampleRate = 44100.0 // Match sample rate of pre-calculated coefficients
        config.preFilterLowCutoff = 8.0
        config.preFilterHighCutoff = 50.0
        // Note: preFilterOrder is part of config but not directly used by PitchDetector's applyPreFilter if coeffs are hardcoded fixed.

        let pitchDetector = PitchDetector(configuration: config)
        pitchDetector.startNewSession() // Initialize filter states

        let testFrequencies: [Float] = [2.0, 4.0, /* low out-of-band */
                                        8.0, 10.0, 25.0, 45.0, 50.0, /* in-band */
                                        70.0, 100.0, 250.0, 500.0 /* high out-of-band */]

        let passbandLowerBound: Float = config.preFilterLowCutoff
        let passbandUpperBound: Float = config.preFilterHighCutoff

        for testFrequency in testFrequencies {
            let sampleRate: Float = config.sampleRate
            let duration: Float = 0.2 // 200ms of audio
            let amplitude: Float = 1.0
            let sampleCount = Int(sampleRate * duration)
            var sineWave = [Float](repeating: 0.0, count: sampleCount)

            for i in 0..<sampleCount {
                let time = Float(i) / sampleRate
                sineWave[i] = amplitude * sin(2.0 * Float.pi * testFrequency * time)
            }

            let inputRMS = calculateRMS(data: sineWave)

            // applyPreFilter was made internal for this test
            let filteredSignal = pitchDetector.applyPreFilter(sineWave)
            let outputRMS = calculateRMS(data: filteredSignal)

            let attenuation = inputRMS > 0 ? outputRMS / inputRMS : 0
            print("Test Freq: \(testFrequency) Hz, Input RMS: \(String(format: "%.4f", inputRMS)), Output RMS: \(String(format: "%.4f", outputRMS)), Attenuation: \(String(format: "%.4f", attenuation))x (\(String(format: "%.2f", 20 * log10(attenuation.isZero ? 0.00001 : attenuation))) dB)")

            if testFrequency >= passbandLowerBound && testFrequency <= passbandUpperBound {
                // In-band
                // Check if outputRMS is not drastically lower than inputRMS (e.g. > 0.5 times, which is -6dB)
                // Butterworth filters are maximally flat, so ripple is low. For an order of 4, it should be quite flat.
                // Allow for some minor attenuation, e.g., down to -3dB (0.707) or -1dB (0.89)
                XCTAssertGreaterThan(outputRMS, inputRMS * 0.707, "Signal at \(testFrequency) Hz should be in passband (>\(String(format: "%.2f", inputRMS * 0.707))) but was \(String(format: "%.2f", outputRMS)).")
                // Check that it's not amplified (allowing for tiny numerical inaccuracies, e.g. 1.001)
                XCTAssertLessThanOrEqual(outputRMS, inputRMS * 1.001, "Signal at \(testFrequency) Hz should not be amplified, but was \(outputRMS) (input \(inputRMS)).")
            } else {
                // Out-of-band
                var expectedAttenuationFactor: Float = 0.3 // Default for moderate out-of-band
                if testFrequency < passbandLowerBound / 2.0 || testFrequency > passbandUpperBound * 2.0 {
                     expectedAttenuationFactor = 0.1 // Expect more attenuation further out (e.g. < 4Hz or > 100Hz)
                }
                 if testFrequency < passbandLowerBound / 4.0 || testFrequency > passbandUpperBound * 4.0 {
                     expectedAttenuationFactor = 0.03 // Expect even more attenuation much further out (e.g. < 2Hz or > 200Hz)
                }

                XCTAssertLessThan(outputRMS, inputRMS * expectedAttenuationFactor, "Signal at \(testFrequency) Hz should be significantly attenuated (<\(String(format: "%.2f", inputRMS * expectedAttenuationFactor))) but was \(String(format: "%.2f", outputRMS)).")
            }
        }
    }

    // Helper function to generate a chunk of data with a specific mean envelope
    private func generateEnergyChunk(meanEnvelope: Float, size: Int) -> [Float] {
        // For simplicity, creating a constant array. Random variation could be added.
        return [Float](repeating: meanEnvelope, count: size)
    }

    func testAdaptiveGatingBehavior() throws {
        var config = AlgorithmConfiguration.standard
        config.adaptiveGatingEnabled = true
        config.sampleRate = 44100.0
        config.downsampleFactorOverride = 45 // Results in downsampledRate of 980 Hz
        config.noiseEstimationLookbackSeconds = 0.3 // Short for testing, buffer size ~0.3 * 980 = 294
        config.noiseGatingThresholdDB = 3.0 // Sensitive for testing (factor of ~1.41)
        config.minSignalEnergyThreshold = 0.0001 // An arbitrary small value

        let pitchDetector = PitchDetector(configuration: config)

        let chunkSize = 100 // Arbitrary number of samples for each chunk passed to computeAutocorrelation

        // --- Scenario 1: Establish low noise, then signal passes gate ---
        print("\nScenario 1: Establish low noise, signal passes gate")
        pitchDetector.startNewSession() // Reset noise estimate and buffers

        // Initial noise estimate should be minSignalEnergyThreshold (0.0001)
        // Dynamic gate threshold = 0.0001 * 10^(3/10) = 0.0001 * 1.995 ~= 0.0001995

        print("Feeding 5 low energy chunks to establish noise floor...")
        let lowEnergy = config.minSignalEnergyThreshold * 1.5 // e.g., 0.00015 (just above min, below initial gate)
        for i in 0..<5 {
            let lowEnergyChunk = generateEnergyChunk(meanEnvelope: lowEnergy, size: chunkSize)
            // computeAutocorrelation made internal for this test
            let _ = pitchDetector.computeAutocorrelation(lowEnergyChunk)
            print("  Chunk \(i+1) (low energy \(lowEnergy)): Noise estimate should update based on this.")
        }
        // After 5 chunks of 0.00015, noise estimate should be around 0.00015
        // New dynamic gate threshold = 0.00015 * 1.995 ~= 0.000299

        print("Feeding 1 high energy chunk, expecting it to pass the gate...")
        let highEnergy = lowEnergy * 100 // e.g., 0.015 (significantly higher)
        let highEnergyChunk = generateEnergyChunk(meanEnvelope: highEnergy, size: chunkSize)
        let pitchResult = pitchDetector.computeAutocorrelation(highEnergyChunk)
        // The primary check here is that the high energy chunk was NOT gated.
        // We expect pitchResult to be 0.0 because generateEnergyChunk does not create a periodic signal
        // that would pass correlation tests. The key is to check logs for gating messages.
        XCTAssertEqual(pitchResult, 0.0, "Pitch should be 0.0 (no actual periodic signal), but this test primarily checks if the signal passed the gate (see logs).")
        print("  High energy chunk (\(highEnergy)) processed. Check logs: should NOT see 'Signal mean ... below dynamic gate'.")

        // --- Scenario 2: Establish moderate noise, signal gets gated ---
        print("\nScenario 2: Establish moderate noise, signal gets gated")
        pitchDetector.startNewSession() // Reset noise estimate

        // Initial noise estimate = 0.0001. Dynamic gate ~0.0001995
        print("Feeding 5 moderate energy chunks to establish higher noise floor...")
        let moderateEnergy = config.minSignalEnergyThreshold * 50 // e.g., 0.005
        for i in 0..<5 {
            let moderateEnergyChunk = generateEnergyChunk(meanEnvelope: moderateEnergy, size: chunkSize)
            let _ = pitchDetector.computeAutocorrelation(moderateEnergyChunk)
            print("  Chunk \(i+1) (moderate energy \(moderateEnergy)): Noise estimate should update.")
        }
        // After 5 chunks of 0.005, noise estimate should be around 0.005
        // New dynamic gate threshold = 0.005 * 1.995 ~= 0.009975

        print("Feeding 1 low energy chunk, expecting it to be gated...")
        // This energy is above minSignalEnergyThreshold (0.0001) but below the new dynamic gate (0.009975)
        let lowEnergyToGate = config.minSignalEnergyThreshold * 20 // e.g., 0.002
        let lowEnergyGatedChunk = generateEnergyChunk(meanEnvelope: lowEnergyToGate, size: chunkSize)
        let gatedPitchResult = pitchDetector.computeAutocorrelation(lowEnergyGatedChunk)
        XCTAssertEqual(gatedPitchResult, 0.0, "Pitch should be 0.0 as signal should be gated.")
        print("  Low energy chunk (\(lowEnergyToGate)) processed. Check logs: should see 'Signal mean ... below dynamic gate'.")

        // --- Scenario 3: Noise estimate adaptation (observational) ---
        print("\nScenario 3: Noise estimate adaptation (observational via logs)")
        pitchDetector.startNewSession()

        let veryLowEnergy = config.minSignalEnergyThreshold * 2.0 // 0.0002
        let mediumEnergyForNoise = config.minSignalEnergyThreshold * 25.0 // 0.0025

        print("Feeding very low energy chunks (initial estimate \(config.minSignalEnergyThreshold))...")
        for i in 0..<5 {
            _ = pitchDetector.computeAutocorrelation(generateEnergyChunk(meanEnvelope: veryLowEnergy, size: chunkSize))
            print("  Chunk \(i+1) (very low energy \(veryLowEnergy)): Observe noise estimate in logs (should be around \(veryLowEnergy)).")
        }

        print("Feeding medium energy chunks (these are not gated, but update noise estimate if no pitch)...")
        for i in 0..<5 {
            _ = pitchDetector.computeAutocorrelation(generateEnergyChunk(meanEnvelope: mediumEnergyForNoise, size: chunkSize))
            print("  Chunk \(i+1) (medium energy \(mediumEnergyForNoise)): Observe noise estimate in logs (should increase towards \(mediumEnergyForNoise)).")
        }

        print("Feeding very low energy chunks again...")
        for i in 0..<5 {
            _ = pitchDetector.computeAutocorrelation(generateEnergyChunk(meanEnvelope: veryLowEnergy, size: chunkSize))
            print("  Chunk \(i+1) (very low energy \(veryLowEnergy)): Observe noise estimate in logs (should decrease back towards \(veryLowEnergy)).")
        }
        print("End of Scenario 3.")
    }

    // MARK: - Noise Test Helper Functions

    private func generateSineWave(frequency: Float, duration: Float, sampleRate: Float, amplitude: Float) -> [Float] {
        let sampleCount = Int(sampleRate * duration)
        var sineWave = [Float](repeating: 0.0, count: sampleCount)
        for i in 0..<sampleCount {
            let time = Float(i) / sampleRate
            sineWave[i] = amplitude * sin(2.0 * Float.pi * frequency * time)
        }
        return sineWave
    }

    private func generateWhiteNoise(duration: Float, sampleRate: Float, amplitude: Float) -> [Float] {
        let sampleCount = Int(duration * sampleRate)
        var noise = [Float](repeating: 0.0, count: sampleCount)
        for i in 0..<sampleCount {
            noise[i] = (Float.random(in: -1.0...1.0)) * amplitude
        }
        return noise
    }

    private func calculateSignalPower(signal: [Float]) -> Float {
        guard !signal.isEmpty else { return 0.0 }
        return signal.map { $0 * $0 }.reduce(0, +) / Float(signal.count)
    }

    private func combineSignalAndNoise(signal: [Float], noise: [Float], targetSNR_dB: Float) -> [Float] {
        guard !signal.isEmpty else { return noise }
        guard !noise.isEmpty else { return signal }

        let signalPower = calculateSignalPower(signal: signal)
        let noisePower = calculateSignalPower(signal: noise)

        guard signalPower > 0 && noisePower > 0 else {
            // If either power is zero, scaling is problematic or meaningless.
            // Combine them directly, or return signal if noise power is zero.
            var combined = [Float](repeating: 0.0, count: signal.count)
            for i in 0..<signal.count {
                combined[i] = signal[i] + (i < noise.count ? noise[i] : 0.0)
            }
            return combined
        }

        let currentSNR_ratio = signalPower / noisePower
        let targetSNR_ratio = pow(10.0, targetSNR_dB / 10.0)

        // If currentSNR_ratio is already targetSNR_ratio, requiredNoiseScalingFactor would be 1.
        // If currentSNR_ratio is 0, this would be 0.
        // If targetSNR_ratio is 0 (targetSNR_dB is -inf), this would be infinity if currentSNR_ratio > 0.
        // For very low targetSNR_dB (e.g. -20dB -> targetSNR_ratio = 0.01),
        // requiredNoiseScalingFactor = sqrt(currentSNR_ratio / 0.01) = sqrt(currentSNR_ratio * 100) = 10 * sqrt(currentSNR_ratio)
        // This means noise needs to be amplified.

        let requiredNoiseScalingFactor = sqrt(currentSNR_ratio / targetSNR_ratio)

        let scaledNoise = noise.map { $0 * requiredNoiseScalingFactor }

        var output = [Float](repeating: 0.0, count: signal.count)
        for i in 0..<signal.count {
            // Ensure noise array is long enough, or tile/truncate. Here, assume noise is at least as long.
            output[i] = signal[i] + (i < scaledNoise.count ? scaledNoise[i] : 0.0)
        }
        return output
    }

    func testPitchDetectionWithWhiteNoise() throws {
        var config = AlgorithmConfiguration.standard
        config.preFilterEnabled = true // Recommended for noisy signals
        config.adaptiveGatingEnabled = true // Crucial for noisy signals
        config.sampleRate = 44100.0
        config.downsampleFactorOverride = 45 // downsampledRate = 980 Hz
        // Adjust gating parameters for realistic scenarios if needed, or keep defaults
        // config.noiseGatingThresholdDB = 6.0 // Example adjustment
        // config.minSignalEnergyThreshold = 1e-7 // Example adjustment

        let pitchDetector = PitchDetector(configuration: config)
        let sampleRate = config.sampleRate

        let targetFrequencies: [Float] = [15.0, 25.0, 35.0] // Hz, within typical detection range
        let snrLevelsDB: [Float] = [20.0, 10.0, 3.0]       // High, Medium, Low SNR
        let signalDuration: Float = 1.0                    // seconds
        let signalAmplitude: Float = 0.5                   // Amplitude for pure sine wave
        let chunkSizeSamples = Int(sampleRate * 0.1)      // Process in 0.1s chunks

        for targetFreq in targetFrequencies {
            for snr_dB in snrLevelsDB {
                pitchDetector.startNewSession() // Reset states for each combination

                let pureSignal = generateSineWave(frequency: targetFreq, duration: signalDuration, sampleRate: sampleRate, amplitude: signalAmplitude)
                // Generate noise with an initial amplitude; it will be scaled by combineSignalAndNoise
                let noise = generateWhiteNoise(duration: signalDuration, sampleRate: sampleRate, amplitude: signalAmplitude)

                let noisySignal = combineSignalAndNoise(signal: pureSignal, noise: noise, targetSNR_dB: snr_dB)

                var detectedPitches: [Float] = []
                var totalSamplesProcessed = 0
                let numChunks = Int(ceil(Float(noisySignal.count) / Float(chunkSizeSamples)))

                print("\n--- Testing Freq: \(targetFreq)Hz, Target SNR: \(snr_dB)dB ---")

                for i in 0..<numChunks {
                    let startIndex = i * chunkSizeSamples
                    let endIndex = min(startIndex + chunkSizeSamples, noisySignal.count)
                    guard startIndex < endIndex else { continue } // Avoid empty slice
                    let chunk = Array(noisySignal[startIndex..<endIndex])

                    if chunk.isEmpty { continue }

                    let pitch = pitchDetector.processChunk(chunk, thisFirstNativeInd: totalSamplesProcessed, numElements: chunk.count)
                    if pitch > 0 {
                        detectedPitches.append(pitch)
                    }
                    totalSamplesProcessed += chunk.count
                }

                print("  Detections: \(detectedPitches.count)/\(numChunks) chunks, Values: \(detectedPitches.map { String(format: "%.1f", $0) })")

                if snr_dB >= 10.0 { // Expect good detection for medium to high SNR
                    XCTAssertGreaterThan(detectedPitches.count, numChunks / 2, "Freq \(targetFreq)Hz, SNR \(snr_dB)dB: Detection rate low (\(detectedPitches.count)/\(numChunks)).")
                    if !detectedPitches.isEmpty {
                        let averageDetectedPitch = detectedPitches.reduce(0, +) / Float(detectedPitches.count)
                        XCTAssertEqual(averageDetectedPitch, targetFreq, accuracy: max(1.0, targetFreq * 0.05), "Freq \(targetFreq)Hz, SNR \(snr_dB)dB: Pitch accuracy poor (avg: \(averageDetectedPitch)).")
                    }
                } else if snr_dB >= 3.0 { // Low SNR, detection may be intermittent but should occur
                    XCTAssertGreaterThanOrEqual(detectedPitches.count, 1, "Freq \(targetFreq)Hz, SNR \(snr_dB)dB: No detections or too few (\(detectedPitches.count)/\(numChunks)).")
                    if !detectedPitches.isEmpty {
                        let averageDetectedPitch = detectedPitches.reduce(0, +) / Float(detectedPitches.count)
                        XCTAssertEqual(averageDetectedPitch, targetFreq, accuracy: max(1.5, targetFreq * 0.075), "Freq \(targetFreq)Hz, SNR \(snr_dB)dB: Pitch accuracy poor (avg: \(averageDetectedPitch)) at low SNR.")
                    }
                }
                // For SNR < 3dB, we might not expect reliable detection, so no strict assert.
            }
        }

        // Test False Positives (Only Noise)
        print("\n--- Testing False Positives with White Noise Only ---")
        pitchDetector.startNewSession()
        let pureNoiseSignalDuration = 2.0 // Test with a bit longer pure noise
        let pureNoise = generateWhiteNoise(duration: pureNoiseSignalDuration, sampleRate: sampleRate, amplitude: signalAmplitude)
        var falsePositiveDetections: [Float] = []
        var totalSamplesProcessedForNoise = 0
        let numNoiseChunks = Int(ceil(Float(pureNoise.count) / Float(chunkSizeSamples)))

        for i in 0..<numNoiseChunks {
            let startIndex = i * chunkSizeSamples
            let endIndex = min(startIndex + chunkSizeSamples, pureNoise.count)
            guard startIndex < endIndex else { continue }
            let chunk = Array(pureNoise[startIndex..<endIndex])

            if chunk.isEmpty { continue }

            let pitch = pitchDetector.processChunk(chunk, thisFirstNativeInd: totalSamplesProcessedForNoise, numElements: chunk.count)
            if pitch > 0 {
                falsePositiveDetections.append(pitch)
            }
            totalSamplesProcessedForNoise += chunk.count
        }

        // For 2 seconds of noise, processed in 0.1s chunks = 20 chunks.
        // Allow a very small number of false positives. E.g., 1 out of 20 chunks (5%).
        let maxAllowedFalsePositiveChunks = numNoiseChunks / 10 // Allow 10% false positive rate for this test
        print("  False Positive Detections: \(falsePositiveDetections.count)/\(numNoiseChunks) chunks. Values: \(falsePositiveDetections.map { String(format: "%.1f", $0) })")
        XCTAssertLessThanOrEqual(falsePositiveDetections.count, maxAllowedFalsePositiveChunks, "Too many false positives (\(falsePositiveDetections.count)/\(numNoiseChunks)) with pure white noise.")
    }
}
