// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		CDEEB61C2DEE14DE0039C8D0 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = CDEEB6032DEE14DB0039C8D0 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = CDEEB60A2DEE14DB0039C8D0;
			remoteInfo = Breath2;
		};
		CDEEB6262DEE14DF0039C8D0 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = CDEEB6032DEE14DB0039C8D0 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = CDEEB60A2DEE14DB0039C8D0;
			remoteInfo = Breath2;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		CDEEB60B2DEE14DB0039C8D0 /* Breath2.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Breath2.app; sourceTree = BUILT_PRODUCTS_DIR; };
		CDEEB61B2DEE14DE0039C8D0 /* Breath2Tests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = Breath2Tests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		CDEEB6252DEE14DF0039C8D0 /* Breath2UITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = Breath2UITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		CDEEB60D2DEE14DB0039C8D0 /* Breath2 */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = Breath2;
			sourceTree = "<group>";
		};
		CDEEB61E2DEE14DE0039C8D0 /* Breath2Tests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = Breath2Tests;
			sourceTree = "<group>";
		};
		CDEEB6282DEE14DF0039C8D0 /* Breath2UITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = Breath2UITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		CDEEB6082DEE14DB0039C8D0 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		CDEEB6182DEE14DE0039C8D0 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		CDEEB6222DEE14DF0039C8D0 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		CDEEB6022DEE14DB0039C8D0 = {
			isa = PBXGroup;
			children = (
				CDEEB60D2DEE14DB0039C8D0 /* Breath2 */,
				CDEEB61E2DEE14DE0039C8D0 /* Breath2Tests */,
				CDEEB6282DEE14DF0039C8D0 /* Breath2UITests */,
				CDEEB60C2DEE14DB0039C8D0 /* Products */,
			);
			sourceTree = "<group>";
		};
		CDEEB60C2DEE14DB0039C8D0 /* Products */ = {
			isa = PBXGroup;
			children = (
				CDEEB60B2DEE14DB0039C8D0 /* Breath2.app */,
				CDEEB61B2DEE14DE0039C8D0 /* Breath2Tests.xctest */,
				CDEEB6252DEE14DF0039C8D0 /* Breath2UITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		CDEEB60A2DEE14DB0039C8D0 /* Breath2 */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = CDEEB62F2DEE14DF0039C8D0 /* Build configuration list for PBXNativeTarget "Breath2" */;
			buildPhases = (
				CDEEB6072DEE14DB0039C8D0 /* Sources */,
				CDEEB6082DEE14DB0039C8D0 /* Frameworks */,
				CDEEB6092DEE14DB0039C8D0 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				CDEEB60D2DEE14DB0039C8D0 /* Breath2 */,
			);
			name = Breath2;
			packageProductDependencies = (
			);
			productName = Breath2;
			productReference = CDEEB60B2DEE14DB0039C8D0 /* Breath2.app */;
			productType = "com.apple.product-type.application";
		};
		CDEEB61A2DEE14DE0039C8D0 /* Breath2Tests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = CDEEB6322DEE14DF0039C8D0 /* Build configuration list for PBXNativeTarget "Breath2Tests" */;
			buildPhases = (
				CDEEB6172DEE14DE0039C8D0 /* Sources */,
				CDEEB6182DEE14DE0039C8D0 /* Frameworks */,
				CDEEB6192DEE14DE0039C8D0 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				CDEEB61D2DEE14DE0039C8D0 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				CDEEB61E2DEE14DE0039C8D0 /* Breath2Tests */,
			);
			name = Breath2Tests;
			packageProductDependencies = (
			);
			productName = Breath2Tests;
			productReference = CDEEB61B2DEE14DE0039C8D0 /* Breath2Tests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		CDEEB6242DEE14DF0039C8D0 /* Breath2UITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = CDEEB6352DEE14DF0039C8D0 /* Build configuration list for PBXNativeTarget "Breath2UITests" */;
			buildPhases = (
				CDEEB6212DEE14DF0039C8D0 /* Sources */,
				CDEEB6222DEE14DF0039C8D0 /* Frameworks */,
				CDEEB6232DEE14DF0039C8D0 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				CDEEB6272DEE14DF0039C8D0 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				CDEEB6282DEE14DF0039C8D0 /* Breath2UITests */,
			);
			name = Breath2UITests;
			packageProductDependencies = (
			);
			productName = Breath2UITests;
			productReference = CDEEB6252DEE14DF0039C8D0 /* Breath2UITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		CDEEB6032DEE14DB0039C8D0 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					CDEEB60A2DEE14DB0039C8D0 = {
						CreatedOnToolsVersion = 16.2;
					};
					CDEEB61A2DEE14DE0039C8D0 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = CDEEB60A2DEE14DB0039C8D0;
					};
					CDEEB6242DEE14DF0039C8D0 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = CDEEB60A2DEE14DB0039C8D0;
					};
				};
			};
			buildConfigurationList = CDEEB6062DEE14DB0039C8D0 /* Build configuration list for PBXProject "Breath2" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = CDEEB6022DEE14DB0039C8D0;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = CDEEB60C2DEE14DB0039C8D0 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				CDEEB60A2DEE14DB0039C8D0 /* Breath2 */,
				CDEEB61A2DEE14DE0039C8D0 /* Breath2Tests */,
				CDEEB6242DEE14DF0039C8D0 /* Breath2UITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		CDEEB6092DEE14DB0039C8D0 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		CDEEB6192DEE14DE0039C8D0 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		CDEEB6232DEE14DF0039C8D0 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		CDEEB6072DEE14DB0039C8D0 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		CDEEB6172DEE14DE0039C8D0 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		CDEEB6212DEE14DF0039C8D0 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		CDEEB61D2DEE14DE0039C8D0 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = CDEEB60A2DEE14DB0039C8D0 /* Breath2 */;
			targetProxy = CDEEB61C2DEE14DE0039C8D0 /* PBXContainerItemProxy */;
		};
		CDEEB6272DEE14DF0039C8D0 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = CDEEB60A2DEE14DB0039C8D0 /* Breath2 */;
			targetProxy = CDEEB6262DEE14DF0039C8D0 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		CDEEB62D2DEE14DF0039C8D0 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		CDEEB62E2DEE14DF0039C8D0 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		CDEEB6302DEE14DF0039C8D0 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"Breath2/Preview Content\"";
				DEVELOPMENT_TEAM = 6R8999W8FA;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "This app needs microphone access to detect audio frequency from the Acapella device for pressure monitoring.";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = Breath.Mo.Breath2;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		CDEEB6312DEE14DF0039C8D0 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"Breath2/Preview Content\"";
				DEVELOPMENT_TEAM = 6R8999W8FA;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "This app needs microphone access to detect audio frequency from the Acapella device for pressure monitoring.";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = Breath.Mo.Breath2;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		CDEEB6332DEE14DF0039C8D0 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 6R8999W8FA;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = Breath.Mo.Breath2Tests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Breath2.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Breath2";
			};
			name = Debug;
		};
		CDEEB6342DEE14DF0039C8D0 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 6R8999W8FA;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = Breath.Mo.Breath2Tests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Breath2.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Breath2";
			};
			name = Release;
		};
		CDEEB6362DEE14DF0039C8D0 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 6R8999W8FA;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = Breath.Mo.Breath2UITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = Breath2;
			};
			name = Debug;
		};
		CDEEB6372DEE14DF0039C8D0 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 6R8999W8FA;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = Breath.Mo.Breath2UITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = Breath2;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		CDEEB6062DEE14DB0039C8D0 /* Build configuration list for PBXProject "Breath2" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				CDEEB62D2DEE14DF0039C8D0 /* Debug */,
				CDEEB62E2DEE14DF0039C8D0 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		CDEEB62F2DEE14DF0039C8D0 /* Build configuration list for PBXNativeTarget "Breath2" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				CDEEB6302DEE14DF0039C8D0 /* Debug */,
				CDEEB6312DEE14DF0039C8D0 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		CDEEB6322DEE14DF0039C8D0 /* Build configuration list for PBXNativeTarget "Breath2Tests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				CDEEB6332DEE14DF0039C8D0 /* Debug */,
				CDEEB6342DEE14DF0039C8D0 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		CDEEB6352DEE14DF0039C8D0 /* Build configuration list for PBXNativeTarget "Breath2UITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				CDEEB6362DEE14DF0039C8D0 /* Debug */,
				CDEEB6372DEE14DF0039C8D0 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = CDEEB6032DEE14DB0039C8D0 /* Project object */;
}
