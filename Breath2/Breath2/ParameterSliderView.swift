//
//  ParameterSliderView.swift
//  Breath2
//
//  Created by <PERSON> on 02/06/2025.
//
//  Reusable parameter slider component for development UI

import SwiftUI

#if DEBUG
struct ParameterSliderView<T: BinaryFloatingPoint>: View where T: Strideable, T.Stride: BinaryFloatingPoint {
    let title: String
    let description: String
    @Binding var value: T
    let range: ClosedRange<T>
    let step: T.Stride
    let unit: String
    let formatSpecifier: String
    let onValueChanged: ((T) -> Void)?
    
    @State private var isEditing = false
    @State private var showingInfo = false
    
    init(
        title: String,
        description: String,
        value: Binding<T>,
        range: ClosedRange<T>,
        step: T.Stride = 0.01,
        unit: String = "",
        formatSpecifier: String = "%.2f",
        onValueChanged: ((T) -> Void)? = nil
    ) {
        self.title = title
        self.description = description
        self._value = value
        self.range = range
        self.step = step
        self.unit = unit
        self.formatSpecifier = formatSpecifier
        self.onValueChanged = onValueChanged
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // Header with title and info button
            HStack {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                
                Button {
                    showingInfo.toggle()
                } label: {
                    Image(systemName: "info.circle")
                        .font(.caption)
                        .foregroundColor(.cyan.opacity(0.7))
                }
                
                Spacer()
                
                // Current value display
                Text(String(format: formatSpecifier, Double(value)) + unit)
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(isEditing ? .cyan : .gray)
                    .animation(.easeInOut(duration: 0.2), value: isEditing)
            }
            
            // Slider
            HStack {
                Text(String(format: formatSpecifier, Double(range.lowerBound)))
                    .font(.caption2)
                    .foregroundColor(.gray)
                    .frame(width: 40, alignment: .leading)

                Slider(value: $value, in: range, step: step) { editing in
                    isEditing = editing
                    if !editing {
                        onValueChanged?(value)
                    }
                }
                .accentColor(.cyan)

                Text(String(format: formatSpecifier, Double(range.upperBound)))
                    .font(.caption2)
                    .foregroundColor(.gray)
                    .frame(width: 40, alignment: .trailing)
            }
            
            // Description (when info is shown)
            if showingInfo {
                Text(description)
                    .font(.caption)
                    .foregroundColor(.gray)
                    .padding(.top, 4)
                    .transition(.opacity.combined(with: .scale(scale: 0.95)))
            }
        }
        .padding(.vertical, 8)
        .animation(.easeInOut(duration: 0.3), value: showingInfo)
    }
}

// MARK: - Integer Slider Variant

struct IntParameterSliderView: View {
    let title: String
    let description: String
    @Binding var value: Int
    let range: ClosedRange<Int>
    let unit: String
    let onValueChanged: ((Int) -> Void)?
    
    @State private var isEditing = false
    @State private var showingInfo = false
    
    init(
        title: String,
        description: String,
        value: Binding<Int>,
        range: ClosedRange<Int>,
        unit: String = "",
        onValueChanged: ((Int) -> Void)? = nil
    ) {
        self.title = title
        self.description = description
        self._value = value
        self.range = range
        self.unit = unit
        self.onValueChanged = onValueChanged
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // Header with title and info button
            HStack {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                
                Button {
                    showingInfo.toggle()
                } label: {
                    Image(systemName: "info.circle")
                        .font(.caption)
                        .foregroundColor(.cyan.opacity(0.7))
                }
                
                Spacer()
                
                // Current value display
                Text("\(value)\(unit)")
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(isEditing ? .cyan : .gray)
                    .animation(.easeInOut(duration: 0.2), value: isEditing)
            }
            
            // Slider
            HStack {
                Text("\(range.lowerBound)")
                    .font(.caption2)
                    .foregroundColor(.gray)
                    .frame(width: 30, alignment: .leading)
                
                Slider(value: Binding(
                    get: { Double(value) },
                    set: { value = Int($0.rounded()) }
                ), in: Double(range.lowerBound)...Double(range.upperBound), step: 1) { editing in
                    isEditing = editing
                    if !editing {
                        onValueChanged?(value)
                    }
                }
                .accentColor(.cyan)
                
                Text("\(range.upperBound)")
                    .font(.caption2)
                    .foregroundColor(.gray)
                    .frame(width: 30, alignment: .trailing)
            }
            
            // Description (when info is shown)
            if showingInfo {
                Text(description)
                    .font(.caption)
                    .foregroundColor(.gray)
                    .padding(.top, 4)
                    .transition(.opacity.combined(with: .scale(scale: 0.95)))
            }
        }
        .padding(.vertical, 8)
        .animation(.easeInOut(duration: 0.3), value: showingInfo)
    }
}

// MARK: - Boolean Toggle Variant

struct BoolParameterToggleView: View {
    let title: String
    let description: String
    @Binding var value: Bool
    let onValueChanged: ((Bool) -> Void)?
    
    @State private var showingInfo = false
    
    init(
        title: String,
        description: String,
        value: Binding<Bool>,
        onValueChanged: ((Bool) -> Void)? = nil
    ) {
        self.title = title
        self.description = description
        self._value = value
        self.onValueChanged = onValueChanged
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // Header with title and toggle
            HStack {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                
                Button {
                    showingInfo.toggle()
                } label: {
                    Image(systemName: "info.circle")
                        .font(.caption)
                        .foregroundColor(.cyan.opacity(0.7))
                }
                
                Spacer()
                
                Toggle("", isOn: $value)
                    .toggleStyle(SwitchToggleStyle(tint: .cyan))
                    .onChange(of: value) { _, newValue in
                        onValueChanged?(newValue)
                    }
            }
            
            // Description (when info is shown)
            if showingInfo {
                Text(description)
                    .font(.caption)
                    .foregroundColor(.gray)
                    .padding(.top, 4)
                    .transition(.opacity.combined(with: .scale(scale: 0.95)))
            }
        }
        .padding(.vertical, 8)
        .animation(.easeInOut(duration: 0.3), value: showingInfo)
    }
}

// MARK: - Parameter Group Container

struct ParameterGroupView<Content: View>: View {
    let title: String
    let content: Content
    
    init(title: String, @ViewBuilder content: () -> Content) {
        self.title = title
        self.content = content()
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(title)
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.cyan)
                .padding(.bottom, 4)
            
            VStack(alignment: .leading, spacing: 8) {
                content
            }
        }
        .padding()
        .background(Color.gray.opacity(0.05))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.cyan.opacity(0.2), lineWidth: 1)
        )
    }
}

#endif

// MARK: - Preview

#Preview {
    #if DEBUG
    VStack(spacing: 20) {
        ParameterSliderView(
            title: "Sample Rate",
            description: "Audio sampling frequency in Hz. Higher values provide better accuracy but use more CPU.",
            value: .constant(44100.0),
            range: 8000.0...48000.0,
            step: 100.0,
            unit: " Hz",
            formatSpecifier: "%.0f"
        ) { newValue in
            print("Sample rate changed to: \(newValue)")
        }
        
        IntParameterSliderView(
            title: "Min Frequency",
            description: "Minimum detectable frequency in Hz. Lower values may increase false positives.",
            value: .constant(7),
            range: 5...15,
            unit: " Hz"
        ) { newValue in
            print("Min frequency changed to: \(newValue)")
        }
        
        BoolParameterToggleView(
            title: "Pre-filter Enabled",
            description: "Enable pre-filtering to reduce noise in the audio signal.",
            value: .constant(true)
        ) { newValue in
            print("Pre-filter enabled changed to: \(newValue)")
        }
    }
    .padding()
    .background(Color.black)
    #else
    Text("Parameter sliders only available in DEBUG builds")
    #endif
}
