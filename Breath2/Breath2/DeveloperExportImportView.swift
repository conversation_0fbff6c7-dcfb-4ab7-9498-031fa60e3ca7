//
//  DeveloperExportImportView.swift
//  Breath2
//
//  Created by Mo on 02/06/2025.
//
//  Export and import configurations for testing and sharing

import SwiftUI

#if DEBUG
struct DeveloperExportImportView: View {
    @ObservedObject var configManager: ConfigurationManager
    @Environment(\.dismiss) private var dismiss
    @State private var exportedJSON = ""
    @State private var importJSON = ""
    @State private var showingExportSheet = false
    @State private var showingImportAlert = false
    @State private var importAlertMessage = ""
    @State private var importSuccess = false
    
    var body: some View {
        NavigationView {
            ZStack {
                // Professional dark background
                LinearGradient(
                    colors: [
                        Color(red: 0.05, green: 0.05, blue: 0.08),
                        Color(red: 0.08, green: 0.08, blue: 0.12),
                        Color(red: 0.10, green: 0.10, blue: 0.15)
                    ],
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: 24) {
                        // Export Section
                        exportSection
                        
                        // Import Section
                        importSection
                        
                        // Quick Share Section
                        quickShareSection
                        
                        Spacer(minLength: 50)
                    }
                    .padding(.horizontal, 16)
                    .padding(.top, 10)
                }
            }
            .navigationTitle("Export/Import")
            .navigationBarTitleDisplayMode(.large)
            .toolbarBackground(Color(red: 0.05, green: 0.05, blue: 0.08), for: .navigationBar)
            .toolbarColorScheme(.dark, for: .navigationBar)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                    .fontWeight(.semibold)
                }
            }
        }
        .sheet(isPresented: $showingExportSheet) {
            exportShareSheet
        }
        .alert(importSuccess ? "Import Successful" : "Import Failed", isPresented: $showingImportAlert) {
            Button("OK") { }
        } message: {
            Text(importAlertMessage)
        }
    }
    
    // MARK: - Export Section
    
    private var exportSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Export Configuration")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.white)
            
            Text("Export your current configuration as JSON for sharing or backup.")
                .font(.subheadline)
                .foregroundColor(.gray)
            
            // Current Configuration Summary
            VStack(alignment: .leading, spacing: 12) {
                Text("Current Configuration")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.cyan)
                
                configurationSummaryView
            }
            
            // Export Buttons
            HStack(spacing: 12) {
                Button(action: {
                    exportToClipboard()
                }) {
                    HStack {
                        Image(systemName: "doc.on.clipboard")
                        Text("Copy")
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(Color.cyan)
                    .foregroundColor(.white)
                    .cornerRadius(8)
                }

                Button(action: {
                    exportForSharing()
                }) {
                    HStack {
                        Image(systemName: "square.and.arrow.up")
                        Text("Share")
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(8)
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    // MARK: - Import Section
    
    private var importSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Import Configuration")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.white)
            
            Text("Paste a JSON configuration to import and apply it.")
                .font(.subheadline)
                .foregroundColor(.gray)
            
            // JSON Input
            VStack(alignment: .leading, spacing: 8) {
                Text("Configuration JSON")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.cyan)
                
                TextEditor(text: $importJSON)
                    .frame(minHeight: 120)
                    .padding(8)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
                    .font(.system(.caption, design: .monospaced))
            }
            
            // Import Buttons
            HStack(spacing: 12) {
                Button(action: {
                    pasteFromClipboard()
                }) {
                    HStack {
                        Image(systemName: "doc.on.clipboard.fill")
                        Text("Paste")
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(Color.orange)
                    .foregroundColor(.white)
                    .cornerRadius(8)
                }

                Button(action: {
                    importConfiguration()
                }) {
                    HStack {
                        Image(systemName: "square.and.arrow.down")
                        Text("Import")
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(Color.green)
                    .foregroundColor(.white)
                    .cornerRadius(8)
                }
                .disabled(importJSON.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    // MARK: - Quick Share Section
    
    private var quickShareSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Quick Share Presets")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.white)
            
            Text("Quickly share common configurations with team members.")
                .font(.subheadline)
                .foregroundColor(.gray)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                quickShareButton("Paper Spec", preset: .standard)
                quickShareButton("Real-time", preset: .realTimeFeedback)
                quickShareButton("High Accuracy", preset: .highAccuracy)
                quickShareButton("Research", preset: .research)
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    // MARK: - Export Share Sheet
    
    private var exportShareSheet: some View {
        VStack {
            if #available(iOS 16.0, *) {
                ShareLink(
                    item: exportedJSON,
                    subject: Text("Algorithm Configuration"),
                    message: Text("Breath2 Algorithm Configuration")
                ) {
                    VStack {
                        Image(systemName: "square.and.arrow.up")
                            .font(.largeTitle)
                            .foregroundColor(.cyan)
                        Text("Share Configuration")
                            .font(.headline)
                    }
                    .padding()
                }
            } else {
                // Fallback for older iOS versions
                VStack {
                    Text("Configuration JSON")
                        .font(.headline)
                        .padding()

                    ScrollView {
                        Text(exportedJSON)
                            .font(.system(.caption, design: .monospaced))
                            .padding()
                    }

                    Button("Copy to Clipboard") {
                        UIPasteboard.general.string = exportedJSON
                    }
                    .padding()
                }
            }
        }
    }
    
    // MARK: - Helper Views
    
    private var configurationSummaryView: some View {
        VStack(alignment: .leading, spacing: 8) {
            summaryRow("Sample Rate", String(format: "%.0f", configManager.currentConfiguration.sampleRate) + " Hz")
            summaryRow("Frequency Range", "\(configManager.currentConfiguration.minFreq)-\(configManager.currentConfiguration.maxFreq) Hz")
            summaryRow("Correlation Threshold", String(format: "%.2f", configManager.currentConfiguration.correlationThreshold))
            summaryRow("Buffer Size", String(format: "%.2f", configManager.currentConfiguration.bufferSize) + " s")
            summaryRow("Decay Rate", String(format: "%.2f", configManager.currentConfiguration.decayRate))
            summaryRow("Min Amplitude", String(format: "%.1e", configManager.currentConfiguration.minAmp))
        }
        .padding()
        .background(Color.gray.opacity(0.05))
        .cornerRadius(8)
    }
    
    private func summaryRow(_ title: String, _ value: String) -> some View {
        HStack {
            Text(title)
                .font(.caption)
                .foregroundColor(.gray)
            
            Spacer()
            
            Text(value)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.white)
        }
    }
    
    private func quickShareButton(_ title: String, preset: ConfigurationPreset) -> some View {
        Button {
            sharePreset(preset)
        } label: {
            VStack(spacing: 8) {
                Image(systemName: "square.and.arrow.up")
                    .font(.title2)
                    .foregroundColor(.cyan)
                
                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 12)
            .background(Color.gray.opacity(0.2))
            .cornerRadius(8)
        }
    }
    
    // MARK: - Helper Methods
    
    private func exportToClipboard() {
        do {
            let encoder = JSONEncoder()
            encoder.outputFormatting = .prettyPrinted
            let data = try encoder.encode(configManager.currentConfiguration)
            let jsonString = String(data: data, encoding: .utf8) ?? ""
            UIPasteboard.general.string = jsonString
        } catch {
            print("Failed to export configuration: \(error)")
        }
    }
    
    private func exportForSharing() {
        do {
            let encoder = JSONEncoder()
            encoder.outputFormatting = .prettyPrinted
            let data = try encoder.encode(configManager.currentConfiguration)
            exportedJSON = String(data: data, encoding: .utf8) ?? ""
            showingExportSheet = true
        } catch {
            print("Failed to export configuration: \(error)")
        }
    }
    
    private func pasteFromClipboard() {
        importJSON = UIPasteboard.general.string ?? ""
    }
    
    private func importConfiguration() {
        let success = configManager.importConfiguration(from: importJSON)
        importSuccess = success
        
        if success {
            importAlertMessage = "Configuration imported and applied successfully."
            importJSON = ""
        } else {
            importAlertMessage = "Failed to import configuration. Please check the JSON format and try again."
        }
        
        showingImportAlert = true
    }
    
    private func sharePreset(_ preset: ConfigurationPreset) {
        let config = AlgorithmConfiguration.configuration(for: preset)
        do {
            let encoder = JSONEncoder()
            encoder.outputFormatting = .prettyPrinted
            let data = try encoder.encode(config)
            exportedJSON = String(data: data, encoding: .utf8) ?? ""
            showingExportSheet = true
        } catch {
            print("Failed to export preset: \(error)")
        }
    }
}

#endif

#Preview {
    #if DEBUG
    DeveloperExportImportView(configManager: ConfigurationManager())
    #else
    Text("Export/Import only available in DEBUG builds")
    #endif
}
