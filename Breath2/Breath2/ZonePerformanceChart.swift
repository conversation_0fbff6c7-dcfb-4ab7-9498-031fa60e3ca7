//
//  ZonePerformanceChart.swift
//  Breath2
//
//  Created by <PERSON> on 02/06/2025.
//
//  Compliant chart showing technique zone performance without pressure values
//

import SwiftUI
import Charts

// MARK: - Zone Performance Chart

struct ZonePerformanceChart: View {
    let session: CompletedSession
    
    private var zoneData: [ZoneDataPoint] {
        return [
            ZoneDataPoint(zone: "Good Technique", percentage: session.percentInGoodZone, color: .green),
            ZoneDataPoint(zone: "Low Effort", percentage: session.percentInLowZone, color: .orange),
            ZoneDataPoint(zone: "High Effort", percentage: session.percentInHighZone, color: .red),
            ZoneDataPoint(zone: "No Effort", percentage: session.percentInNoEffortZone, color: .gray)
        ].filter { $0.percentage > 0 } // Only show zones that were used
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Chart title
            Text("Technique Zone Distribution")
                .font(.title3)
                .fontWeight(.semibold)
                .foregroundColor(.white)
            
            // Zone legend
            HStack(spacing: 16) {
                ForEach(zoneData, id: \.zone) { dataPoint in
                    ZoneLegendItem(
                        color: dataPoint.color,
                        label: dataPoint.zone,
                        percentage: dataPoint.percentage
                    )
                }
            }
            .font(.caption)
            
            // Horizontal bar chart
            Chart(zoneData, id: \.zone) { dataPoint in
                BarMark(
                    x: .value("Percentage", dataPoint.percentage),
                    y: .value("Zone", dataPoint.zone)
                )
                .foregroundStyle(dataPoint.color)
                .cornerRadius(4)
            }
            .chartXAxisLabel("Time in Zone (%)")
            .chartYAxis {
                AxisMarks(preset: .extended) { _ in
                    AxisValueLabel()
                        .foregroundStyle(.white.opacity(0.8))
                }
            }
            .chartXAxis {
                AxisMarks { _ in
                    AxisValueLabel()
                        .foregroundStyle(.white.opacity(0.8))
                }
            }
            .frame(height: 150)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.black.opacity(0.2))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.white.opacity(0.1), lineWidth: 1)
                    )
            )
            .padding(.horizontal, 8)
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.05))
        )
    }
}

// MARK: - Supporting Types

struct ZoneDataPoint {
    let zone: String
    let percentage: Double
    let color: Color
}

struct ZoneLegendItem: View {
    let color: Color
    let label: String
    let percentage: Double
    
    var body: some View {
        HStack(spacing: 4) {
            Circle()
                .fill(color)
                .frame(width: 8, height: 8)
            
            Text(label)
                .foregroundColor(.white.opacity(0.8))
            
            Text("(\(Int(percentage))%)")
                .foregroundColor(.white.opacity(0.6))
        }
    }
}

// MARK: - Preview

#Preview {
    ZonePerformanceChart(
        session: CompletedSession(
            date: Date(),
            duration: 300,
            quality: .excellent,
            stepsCompleted: 10,
            totalSteps: 10,
            percentInGoodZone: 70.0,
            percentInLowZone: 20.0,
            percentInHighZone: 10.0,
            percentInNoEffortZone: 0.0,
            feedback: "Great session!"
        )
    )
    .padding()
    .background(Color.black)
}
