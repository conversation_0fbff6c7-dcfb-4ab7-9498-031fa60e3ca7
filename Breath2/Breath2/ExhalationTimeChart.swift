//
//  ExhalationTimeChart.swift
//  Breath2
//
//  Created by <PERSON> on 02/06/2025.
//

import SwiftUI
import Charts

// MARK: - Exhalation Time Chart

struct ExhalationTimeChart: View {
    let session: CompletedSession

    // MARK: - Duration Zone Constants
    private let optimalDurationThreshold: Double = 3.0  // ≥3.0s is optimal
    private let maxYAxisValue: Double = 10.0           // Fixed Y-axis maximum
    private let minYAxisValue: Double = 0.0            // Fixed Y-axis minimum

    private var exhalationData: [ExhalationDataPoint] {
        let durations = session.exhalationDurations
        return durations.enumerated().map { index, duration in
            ExhalationDataPoint(
                exhalationNumber: index + 1,
                duration: duration,
                isOptimal: duration >= optimalDurationThreshold
            )
        }
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Chart title and legend
            VStack(alignment: .leading, spacing: 8) {
                Text("Exhalation Duration Compliance")
                    .font(.title3)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                
                // Legend
                HStack(spacing: 16) {
                    LegendItem(color: .green, label: "≥3.0s (Optimal)")
                    LegendItem(color: .orange, label: "<3.0s (Too Short)")
                }
                .font(.caption)
            }
            
            // Modern chart with zone coloring
            VStack(spacing: 8) {
                modernDurationChart
                    .frame(height: 240)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color.black.opacity(0.2))
                            .overlay(
                                RoundedRectangle(cornerRadius: 16)
                                    .stroke(Color.white.opacity(0.1), lineWidth: 1)
                            )
                    )
                    .padding(.horizontal, 4)

                // X-axis label
                Text("Exhalation Number")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
                    .padding(.top, 4)
            }
            
            // Summary metrics
            exhalationSummary
        }
    }

    private var modernDurationChart: some View {
        Chart {
            // BACKGROUND ZONES - Draw first so they appear behind the line
            // Below optimal zone (0 to 3.0 seconds) - Orange/Amber
            RectangleMark(
                xStart: .value("Start", 1),
                xEnd: .value("End", exhalationData.count),
                yStart: .value("Zone Start", minYAxisValue),
                yEnd: .value("Zone End", optimalDurationThreshold)
            )
            .foregroundStyle(.orange.opacity(0.15))
            .zIndex(0)

            // Optimal zone (3.0 to 10.0 seconds) - Green
            RectangleMark(
                xStart: .value("Start", 1),
                xEnd: .value("End", exhalationData.count),
                yStart: .value("Zone Start", optimalDurationThreshold),
                yEnd: .value("Zone End", maxYAxisValue)
            )
            .foregroundStyle(.green.opacity(0.15))
            .zIndex(0)

            // Optimal duration threshold line
            RuleMark(y: .value("Optimal Threshold", optimalDurationThreshold))
                .foregroundStyle(.green.opacity(0.6))
                .lineStyle(StrokeStyle(lineWidth: 2, dash: [5, 5]))
                .zIndex(1)

            // Duration line with zone-based coloring
            ForEach(Array(exhalationData.enumerated()), id: \.offset) { index, dataPoint in
                LineMark(
                    x: .value("Exhalation", dataPoint.exhalationNumber),
                    y: .value("Duration", dataPoint.duration)
                )
                .foregroundStyle(colorForDuration(dataPoint.duration))
                .lineStyle(StrokeStyle(lineWidth: 3, lineCap: .round, lineJoin: .round))
                .zIndex(2)

                // Add data points for better visibility
                PointMark(
                    x: .value("Exhalation", dataPoint.exhalationNumber),
                    y: .value("Duration", dataPoint.duration)
                )
                .foregroundStyle(colorForDuration(dataPoint.duration))
                .symbolSize(16)
                .zIndex(3)
            }
        }
        .chartXAxis {
            AxisMarks(values: .stride(by: 1)) { value in
                AxisGridLine(stroke: StrokeStyle(lineWidth: 0.5))
                    .foregroundStyle(.white.opacity(0.2))
                AxisTick()
                    .foregroundStyle(.white.opacity(0.4))
                AxisValueLabel(centered: true) {
                    if let exhalationNum = value.as(Int.self) {
                        Text("\(exhalationNum)")
                            .foregroundColor(.white.opacity(0.7))
                            .font(.caption)
                    }
                }
            }
        }
        .chartXScale(domain: 1...max(1, exhalationData.count))
        .chartYAxis {
            AxisMarks(values: .stride(by: 1.0)) { value in
                AxisGridLine(stroke: StrokeStyle(lineWidth: 0.5))
                    .foregroundStyle(.white.opacity(0.2))
                AxisTick()
                    .foregroundStyle(.white.opacity(0.4))
                AxisValueLabel {
                    if let duration = value.as(Double.self) {
                        Text("\(String(format: "%.0f", duration))s")
                            .foregroundColor(.white.opacity(0.7))
                            .font(.caption)
                    }
                }
            }
        }
        .chartYScale(domain: minYAxisValue...maxYAxisValue)
        .padding(.all, 16)
    }

    private func colorForDuration(_ duration: Double) -> Color {
        if duration >= optimalDurationThreshold {
            return .green
        } else {
            return .orange
        }
    }

    private var exhalationSummary: some View {
        HStack(spacing: 16) {
            ExhalationMetricCard(
                title: "Optimal Duration",
                value: "\(optimalExhalationsCount)/\(session.stepsCompleted)",
                subtitle: "\(optimalPercentage)%",
                color: .green
            )
            
            ExhalationMetricCard(
                title: "Average Duration",
                value: "\(String(format: "%.1f", session.averageExhalationDuration))s",
                subtitle: "Target: 3.0s+",
                color: session.averageExhalationDuration >= 3.0 ? .green : .orange
            )
            
            ExhalationMetricCard(
                title: "Consistency",
                value: consistencyRating,
                subtitle: "Duration variance",
                color: consistencyColor
            )
        }
    }
    
    private var optimalExhalationsCount: Int {
        exhalationData.filter { $0.isOptimal }.count
    }
    
    private var optimalPercentage: Int {
        guard session.stepsCompleted > 0 else { return 0 }
        return Int(Double(optimalExhalationsCount) / Double(session.stepsCompleted) * 100)
    }
    
    private var consistencyRating: String {
        let durations = session.exhalationDurations
        guard durations.count > 1 else { return "N/A" }
        
        let average = durations.reduce(0, +) / Double(durations.count)
        let variance = durations.map { pow($0 - average, 2) }.reduce(0, +) / Double(durations.count)
        let standardDeviation = sqrt(variance)
        
        if standardDeviation < 0.5 {
            return "Excellent"
        } else if standardDeviation < 1.0 {
            return "Good"
        } else if standardDeviation < 1.5 {
            return "Fair"
        } else {
            return "Variable"
        }
    }
    
    private var consistencyColor: Color {
        switch consistencyRating {
        case "Excellent": return .green
        case "Good": return .blue
        case "Fair": return .orange
        default: return .red
        }
    }
}

// MARK: - Supporting Views

struct ExhalationDataPoint {
    let exhalationNumber: Int
    let duration: Double
    let isOptimal: Bool
}

struct LegendItem: View {
    let color: Color
    let label: String
    
    var body: some View {
        HStack(spacing: 4) {
            Circle()
                .fill(color)
                .frame(width: 8, height: 8)
            Text(label)
                .foregroundColor(.white.opacity(0.7))
        }
    }
}

struct ExhalationMetricCard: View {
    let title: String
    let value: String
    let subtitle: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 4) {
            Text(value)
                .font(.system(size: 16, weight: .bold, design: .rounded))
                .foregroundColor(color)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.white.opacity(0.7))
            
            Text(subtitle)
                .font(.caption2)
                .foregroundColor(.white.opacity(0.7))
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(color.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(color.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

#Preview {
    // Create sample session data for preview with deterministic values
    let sampleReadings = (1...50).map { i in
        let pressure = 15.0 + sin(Double(i) * 0.3) * 5.0 // Deterministic variation
        return StoredPressureReading(
            timestamp: Date().addingTimeInterval(Double(i)),
            pressure: max(8.0, min(25.0, pressure)),
            step: i / 5 + 1
        )
    }

    // Create realistic sample breath durations for preview
    let sampleBreathDurations: [Double] = [
        7.2, 6.8, 7.5, 6.9, 7.1, 8.2, 6.5, 7.8, 7.0, 6.7
    ]

    let sampleSession = CompletedSession(
        date: Date(),
        duration: 180,
        quality: .good,
        stepsCompleted: 10,
        totalSteps: 10,
        averagePressure: 15.0,
        maxPressure: 22.0,
        minPressure: 8.0,
        pressureReadings: sampleReadings,
        feedback: "Good session!",
        actualBreathDurations: sampleBreathDurations
    )

    ExhalationTimeChart(session: sampleSession)
        .padding()
        .background(Color.black)
}
