//
//  DeveloperPresetManagerView.swift
//  Breath2
//
//  Created by Mo on 02/06/2025.
//
//  Preset management for development configurations

import SwiftUI

#if DEBUG
struct DeveloperPresetManagerView: View {
    @ObservedObject var configManager: ConfigurationManager
    @Environment(\.dismiss) private var dismiss
    @State private var customPresets: [CustomPreset] = []
    @State private var showingCreatePreset = false
    @State private var newPresetName = ""
    @State private var selectedPreset: CustomPreset?
    @State private var showingDeleteAlert = false
    
    struct CustomPreset: Identifiable, Codable {
        var id = UUID()
        let name: String
        let configuration: AlgorithmConfiguration
        let createdDate: Date
        let description: String
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                // Professional dark background
                LinearGradient(
                    colors: [
                        Color(red: 0.05, green: 0.05, blue: 0.08),
                        Color(red: 0.08, green: 0.08, blue: 0.12),
                        Color(red: 0.10, green: 0.10, blue: 0.15)
                    ],
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: 24) {
                        // Built-in Presets Section
                        builtInPresetsSection
                        
                        // Custom Presets Section
                        customPresetsSection
                        
                        // Create New Preset Button
                        createPresetButton
                        
                        Spacer(minLength: 50)
                    }
                    .padding(.horizontal, 16)
                    .padding(.top, 10)
                }
            }
            .navigationTitle("Preset Manager")
            .navigationBarTitleDisplayMode(.large)
            .toolbarBackground(Color(red: 0.05, green: 0.05, blue: 0.08), for: .navigationBar)
            .toolbarColorScheme(.dark, for: .navigationBar)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                    .fontWeight(.semibold)
                }
            }
        }
        .onAppear {
            loadCustomPresets()
        }
        .sheet(isPresented: $showingCreatePreset) {
            createPresetSheet
        }
        .alert("Delete Preset", isPresented: $showingDeleteAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Delete", role: .destructive) {
                if let preset = selectedPreset {
                    deletePreset(preset)
                }
            }
        } message: {
            Text("Are you sure you want to delete '\(selectedPreset?.name ?? "")'? This action cannot be undone.")
        }
    }
    
    // MARK: - Built-in Presets Section
    
    private var builtInPresetsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Built-in Presets")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.white)
            
            VStack(spacing: 12) {
                presetCard(
                    title: "Paper Compliant",
                    description: "Strict adherence to research paper specifications",
                    isBuiltIn: true
                ) {
                    configManager.applyPreset(.standard)
                }
                
                presetCard(
                    title: "Real-time Feedback",
                    description: "Optimized for fast response and low latency",
                    isBuiltIn: true
                ) {
                    configManager.applyPreset(.realTimeFeedback)
                }
                
                presetCard(
                    title: "High Accuracy",
                    description: "Maximum precision with higher processing overhead",
                    isBuiltIn: true
                ) {
                    configManager.applyPreset(.highAccuracy)
                }
                
                presetCard(
                    title: "Research Mode",
                    description: "Extended analysis with data logging enabled",
                    isBuiltIn: true
                ) {
                    configManager.applyPreset(.research)
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    // MARK: - Custom Presets Section
    
    private var customPresetsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Custom Presets")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.white)
            
            if customPresets.isEmpty {
                VStack(spacing: 12) {
                    Image(systemName: "folder")
                        .font(.system(size: 40))
                        .foregroundColor(.gray)
                    
                    Text("No custom presets")
                        .font(.subheadline)
                        .foregroundColor(.gray)
                    
                    Text("Create a preset to save your current configuration")
                        .font(.caption)
                        .foregroundColor(.gray.opacity(0.7))
                        .multilineTextAlignment(.center)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 40)
            } else {
                VStack(spacing: 12) {
                    ForEach(customPresets) { preset in
                        customPresetCard(preset)
                    }
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    // MARK: - Create Preset Button
    
    private var createPresetButton: some View {
        Button {
            showingCreatePreset = true
        } label: {
            HStack {
                Image(systemName: "plus.circle.fill")
                    .font(.title2)
                
                Text("Create New Preset")
                    .font(.headline)
                    .fontWeight(.semibold)
            }
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding(.vertical, 16)
            .background(Color.cyan)
            .cornerRadius(12)
        }
    }
    
    // MARK: - Create Preset Sheet
    
    private var createPresetSheet: some View {
        NavigationView {
            VStack(spacing: 24) {
                VStack(alignment: .leading, spacing: 12) {
                    Text("Preset Name")
                        .font(.headline)
                        .foregroundColor(.white)
                    
                    TextField("Enter preset name", text: $newPresetName)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                }
                
                VStack(alignment: .leading, spacing: 12) {
                    Text("Current Configuration Summary")
                        .font(.headline)
                        .foregroundColor(.white)
                    
                    configurationSummaryView
                }
                
                Spacer()
            }
            .padding()
            .background(Color.black)
            .navigationTitle("Create Preset")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        showingCreatePreset = false
                        newPresetName = ""
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        saveNewPreset()
                    }
                    .disabled(newPresetName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                }
            }
        }
    }
    
    // MARK: - Helper Views
    
    private func presetCard(title: String, description: String, isBuiltIn: Bool, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                    
                    Text(description)
                        .font(.caption)
                        .foregroundColor(.gray)
                        .multilineTextAlignment(.leading)
                }
                
                Spacer()
                
                if isBuiltIn {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.cyan)
                }
            }
            .padding()
            .background(Color.gray.opacity(0.1))
            .cornerRadius(8)
        }
    }
    
    private func customPresetCard(_ preset: CustomPreset) -> some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(preset.name)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                
                Text(preset.description)
                    .font(.caption)
                    .foregroundColor(.gray)
                
                Text("Created: \(preset.createdDate, style: .date)")
                    .font(.caption2)
                    .foregroundColor(.gray.opacity(0.7))
            }
            
            Spacer()
            
            HStack(spacing: 12) {
                Button("Load") {
                    loadPreset(preset)
                }
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.cyan)
                
                Button("Delete") {
                    selectedPreset = preset
                    showingDeleteAlert = true
                }
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.red)
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(8)
    }
    
    private var configurationSummaryView: some View {
        VStack(alignment: .leading, spacing: 8) {
            summaryRow("Sample Rate", String(format: "%.0f", configManager.currentConfiguration.sampleRate) + " Hz")
            summaryRow("Frequency Range", "\(configManager.currentConfiguration.minFreq)-\(configManager.currentConfiguration.maxFreq) Hz")
            summaryRow("Correlation Threshold", String(format: "%.2f", configManager.currentConfiguration.correlationThreshold))
            summaryRow("Buffer Size", String(format: "%.2f", configManager.currentConfiguration.bufferSize) + " s")
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(8)
    }
    
    private func summaryRow(_ title: String, _ value: String) -> some View {
        HStack {
            Text(title)
                .font(.caption)
                .foregroundColor(.gray)
            
            Spacer()
            
            Text(value)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.white)
        }
    }
    
    // MARK: - Helper Methods
    
    private func loadCustomPresets() {
        if let data = UserDefaults.standard.data(forKey: "DeveloperCustomPresets") {
            do {
                customPresets = try JSONDecoder().decode([CustomPreset].self, from: data)
            } catch {
                print("Failed to load custom presets: \(error)")
                customPresets = []
            }
        }
    }
    
    private func saveCustomPresets() {
        do {
            let data = try JSONEncoder().encode(customPresets)
            UserDefaults.standard.set(data, forKey: "DeveloperCustomPresets")
        } catch {
            print("Failed to save custom presets: \(error)")
        }
    }
    
    private func saveNewPreset() {
        let preset = CustomPreset(
            name: newPresetName.trimmingCharacters(in: .whitespacesAndNewlines),
            configuration: configManager.currentConfiguration,
            createdDate: Date(),
            description: generateDescription(for: configManager.currentConfiguration)
        )
        
        customPresets.append(preset)
        saveCustomPresets()
        
        showingCreatePreset = false
        newPresetName = ""
    }
    
    private func loadPreset(_ preset: CustomPreset) {
        configManager.currentConfiguration = preset.configuration
        configManager.saveConfiguration()
    }
    
    private func deletePreset(_ preset: CustomPreset) {
        customPresets.removeAll { $0.id == preset.id }
        saveCustomPresets()
    }
    
    private func generateDescription(for config: AlgorithmConfiguration) -> String {
        return "Freq: \(config.minFreq)-\(config.maxFreq)Hz, Corr: \(String(format: "%.2f", config.correlationThreshold)), Buffer: \(String(format: "%.2f", config.bufferSize))s"
    }
}

#endif

#Preview {
    #if DEBUG
    DeveloperPresetManagerView(configManager: ConfigurationManager())
    #else
    Text("Preset manager only available in DEBUG builds")
    #endif
}
