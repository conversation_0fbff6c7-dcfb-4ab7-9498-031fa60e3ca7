//
//  AudioManager.swift
//  Breath2
//
//  Created by Mo on 02/06/2025.
//

import Foundation
import AVFoundation
import Combine

class AudioManager: ObservableObject {
    // MARK: - Published Properties
    @Published var currentFrequency: Double = 0.0
    @Published var currentZone: ExhalationZone = .noEffort
    @Published var audioLevel: Float = 0.0
    @Published var hasPermission: Bool = false

    // Breath detection
    @Published var breathDetector = BreathDetector()

    // MARK: - Private Properties
    private var audioEngine: AVAudioEngine?
    private var inputNode: AVAudioInputNode?
    private var pitchDetector: PitchDetector?
    private var techniqueAnalyzer: TechniqueAnalyzer?
    // Audio configuration
    private var sampleRate: Double = 44100.0
    private var bufferSize: AVAudioFrameCount = 4410 // Default buffer size

    // MARK: - Pipeline State Management (Paper Specification)
    private var totalSamplesProcessed: Int = 0

    #if DEBUG
    // Configuration manager for development mode
    private var configManager: ConfigurationManager?
    #endif  // Track absolute sample position across chunks

    // MARK: - Initialization
    init() {
        setupAudioComponents()

        #if DEBUG
        // Initialize configuration manager for development mode
        configManager = ConfigurationManager()
        #endif
    }

    #if DEBUG
    // Development-only method to update configuration in real-time
    func updateConfiguration(_ configuration: AlgorithmConfiguration) {
        // Stop current recording if active
        let wasRecording = audioEngine?.isRunning ?? false
        if wasRecording {
            stopRecording()
        }

        // Update configuration
        configManager?.currentConfiguration = configuration

        // Recreate audio components with new configuration
        setupAudioComponents()

        // Restart recording if it was active
        if wasRecording {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                self.startRecording()
            }
        }
    }
    #endif

    /// **Cleans up** audio resources when the manager is deallocated.
    deinit {
        stopRecording()
        audioEngine = nil
        inputNode = nil
        pitchDetector = nil
        techniqueAnalyzer = nil
        print("🧹 AudioManager deallocated - resources cleaned up")
    }
    
    // MARK: - Public Methods
    func requestPermission() {
        #if os(iOS)
        if #available(iOS 17.0, *) {
            AVAudioApplication.requestRecordPermission { [weak self] granted in
                DispatchQueue.main.async {
                    self?.hasPermission = granted
                    if granted {
                        self?.setupAudioSession()
                    }
                }
            }
        } else {
            AVAudioSession.sharedInstance().requestRecordPermission { [weak self] granted in
                DispatchQueue.main.async {
                    self?.hasPermission = granted
                    if granted {
                        self?.setupAudioSession()
                    }
                }
            }
        }
        #endif
    }
    
    func startRecording() {
        print("🎬 startRecording called")
        guard hasPermission else {
            print("❌ Audio permission not granted")
            return
        }

        // Reset breath detection for new session
        breathDetector.reset()

        // Reset pipeline state for new session (Paper specification)
        totalSamplesProcessed = 0

        print("🔧 Setting up audio engine...")
        setupAudioEngine()

        // Start new session as specified in paper
        pitchDetector?.startNewSession()

        do {
            try audioEngine?.start()
            print("✅ Audio engine started successfully")
        } catch {
            print("❌ Failed to start audio engine: \(error)")
        }
    }

    func stopRecording() {
        print("🛑 stopRecording called")
        audioEngine?.stop()
        if let inputNode = audioEngine?.inputNode {
            inputNode.removeTap(onBus: 0)
        }
        print("✅ Audio engine stopped")
    }
    
    // MARK: - Private Methods
    private func setupAudioComponents() {
        #if DEBUG
        // Use configuration manager in debug mode
        if let config = configManager?.currentConfiguration {
            pitchDetector = PitchDetector(configuration: config)
            sampleRate = Double(config.sampleRate)
            bufferSize = AVAudioFrameCount(sampleRate * Double(config.bufferSize))
        } else {
            // Fallback to default configuration
            setupDefaultComponents()
        }
        #else
        // Use default configuration in release mode
        setupDefaultComponents()
        #endif

        techniqueAnalyzer = TechniqueAnalyzer()
    }

    private func setupDefaultComponents() {
        pitchDetector = PitchDetector(
            sampleRate: Float(sampleRate),
            minFreq: 7,
            maxFreq: 40,
            freqAccuracy: 0.025,
            lowerFormantFreq: 250,
            decayRate: 0.8,
            minAmp: 2.0e-4,
            saveResults: false
        )

        // Use default buffer size
        bufferSize = AVAudioFrameCount(sampleRate * 0.1) // 100ms buffer
    }

    // Configuration management temporarily disabled
    
    private func setupAudioSession() {
        #if os(iOS)
        do {
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(.playAndRecord, mode: .measurement, options: [.defaultToSpeaker, .allowBluetooth])
            try audioSession.setActive(true, options: [])
            print("✅ Audio session configured successfully")
        } catch {
            print("❌ Failed to setup audio session: \(error)")
        }
        #endif
    }
    
    private func setupAudioEngine() {
        audioEngine = AVAudioEngine()
        inputNode = audioEngine?.inputNode

        guard let inputNode = inputNode else {
            print("Failed to get input node")
            return
        }

        let inputFormat = inputNode.outputFormat(forBus: 0)
        print("Input format: \(inputFormat)")

        // Update sample rate based on actual input format
        sampleRate = inputFormat.sampleRate

        // Calculate buffer size for exactly 0.1sec as per paper
        bufferSize = AVAudioFrameCount(sampleRate * 0.1)

        // Recreate pitch detector with correct sample rate
        #if DEBUG
        if let config = configManager?.currentConfiguration {
            var updatedConfig = config
            updatedConfig.sampleRate = Float(sampleRate)
            pitchDetector = PitchDetector(configuration: updatedConfig)
        } else {
            // Fallback to default
            pitchDetector = PitchDetector(
                sampleRate: Float(sampleRate),
                minFreq: 7,
                maxFreq: 40,
                freqAccuracy: 0.025,
                lowerFormantFreq: 250,
                decayRate: 0.8,
                minAmp: 2.0e-4,
                saveResults: false
            )
        }
        #else
        pitchDetector = PitchDetector(
            sampleRate: Float(sampleRate),
            minFreq: 7,
            maxFreq: 40,
            freqAccuracy: 0.025,
            lowerFormantFreq: 250,
            decayRate: 0.8,
            minAmp: 2.0e-4,
            saveResults: false
        )
        #endif

        // Install tap to process audio data
        inputNode.installTap(onBus: 0, bufferSize: bufferSize, format: inputFormat) { [weak self] buffer, time in
            self?.processAudioBuffer(buffer)
        }

        audioEngine?.prepare()
    }
    
    private func processAudioBuffer(_ buffer: AVAudioPCMBuffer) {
        print("📊 processAudioBuffer called - frameLength: \(buffer.frameLength)")

        guard let channelData = buffer.floatChannelData?[0] else {
            print("❌ No channel data available")
            return
        }

        guard let pitchDetector = pitchDetector else {
            print("❌ PitchDetector is nil")
            return
        }

        guard let techniqueAnalyzer = techniqueAnalyzer else {
            print("❌ TechniqueAnalyzer is nil")
            return
        }

        let frameCount = Int(buffer.frameLength)
        let audioData = Array(UnsafeBufferPointer(start: channelData, count: frameCount))

        // Debug: Check if we're getting audio data
        let audioLevel = audioData.map { abs($0) }.max() ?? 0.0
        print("🎤 Audio Level: \(audioLevel), Samples: \(frameCount)")

        // Process audio chunk through pitch detector
        print("🔍 Processing audio through pitch detector...")
        let detectedPitch = pitchDetector.processChunk(audioData)
        print("🎵 Detected pitch: \(detectedPitch) Hz")

        // Classify pitch into qualitative technique zone
        let classifiedZone = techniqueAnalyzer.classifyPitch(detectedPitch)
        print("🎯 Classified zone: \(classifiedZone.rawValue)")

        // Update UI on main thread
        DispatchQueue.main.async { [weak self] in
            self?.currentFrequency = Double(detectedPitch)
            self?.currentZone = classifiedZone
            self?.audioLevel = audioLevel

            // Process breath detection using frequency and audio level signals
            // Note: Breath detection now uses frequency and audio level, not pressure
            self?.breathDetector.processBreathingSignals(
                pressure: 0, // Deprecated parameter, will be removed from BreathDetector
                frequency: detectedPitch,
                audioLevel: audioLevel
            )

            print("🖥️ UI updated - Freq: \(detectedPitch), Zone: \(classifiedZone.rawValue), Breaths: \(self?.breathDetector.breathCount ?? 0)")
        }
    }


}
