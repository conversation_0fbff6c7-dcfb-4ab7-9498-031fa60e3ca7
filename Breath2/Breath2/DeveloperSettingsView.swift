//
//  DeveloperSettingsView.swift
//  Breath2
//
//  Created by Mo on 02/06/2025.
//
//  Development-only UI for real-time algorithm parameter adjustment

import SwiftUI

#if DEBUG
struct DeveloperSettingsView: View {
    @StateObject private var configManager = ConfigurationManager()
    @StateObject private var audioManager = AudioManager()
    @State private var selectedCategory: ParameterCategory = .frequency
    @State private var showingPresetManager = false
    @State private var showingExportImport = false
    @State private var isLiveMode = false
    
    enum ParameterCategory: String, CaseIterable {
        case frequency = "Frequency"
        case correlation = "Correlation"
        case audio = "Audio"
        case performance = "Performance"
        case advanced = "Advanced"
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                // Professional dark background
                LinearGradient(
                    colors: [
                        Color(red: 0.05, green: 0.05, blue: 0.08),
                        Color(red: 0.08, green: 0.08, blue: 0.12),
                        Color(red: 0.10, green: 0.10, blue: 0.15)
                    ],
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: 24) {
                        // Header Section
                        headerSection
                        
                        // Live Mode Toggle
                        liveModeSection
                        
                        // Category Picker
                        categoryPickerSection
                        
                        // Parameter Controls
                        parameterControlsSection
                        
                        // Quick Actions
                        quickActionsSection
                        
                        // Live Feedback (if enabled)
                        if isLiveMode {
                            liveFeedbackSection
                        }
                        
                        Spacer(minLength: 100)
                    }
                    .padding(.horizontal, 16)
                    .padding(.top, 10)
                }
            }
            .navigationTitle("Developer Settings")
            .navigationBarTitleDisplayMode(.large)
            .toolbarBackground(Color(red: 0.05, green: 0.05, blue: 0.08), for: .navigationBar)
            .toolbarColorScheme(.dark, for: .navigationBar)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu {
                        Button("Preset Manager") {
                            showingPresetManager = true
                        }
                        Button("Export/Import") {
                            showingExportImport = true
                        }
                        Button("Reset to Paper Spec") {
                            resetToPaperSpec()
                        }
                    } label: {
                        Image(systemName: "ellipsis.circle")
                            .foregroundColor(.cyan)
                    }
                }
            }
        }
        .sheet(isPresented: $showingPresetManager) {
            DeveloperPresetManagerView(configManager: configManager)
        }
        .sheet(isPresented: $showingExportImport) {
            DeveloperExportImportView(configManager: configManager)
        }
        .onAppear {
            // Initialize with current configuration
            if isLiveMode {
                audioManager.requestPermission()
            }
        }
    }
    
    // MARK: - Header Section
    
    private var headerSection: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: "wrench.and.screwdriver.fill")
                    .font(.system(size: 32))
                    .foregroundColor(.cyan)
                
                VStack(alignment: .leading) {
                    Text("Developer Settings")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                    
                    Text("Real-time algorithm tuning")
                        .font(.subheadline)
                        .foregroundColor(.gray)
                }
                
                Spacer()
            }
            
            // Warning Banner
            HStack {
                Image(systemName: "exclamationmark.triangle.fill")
                    .foregroundColor(.orange)
                
                Text("DEBUG BUILD ONLY - Not available in production")
                    .font(.caption)
                    .foregroundColor(.orange)
                
                Spacer()
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(Color.orange.opacity(0.1))
            .cornerRadius(8)
        }
    }
    
    // MARK: - Live Mode Section
    
    private var liveModeSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Live Testing Mode")
                .font(.headline)
                .foregroundColor(.white)
            
            Toggle("Enable Real-time Audio Processing", isOn: $isLiveMode)
                .toggleStyle(SwitchToggleStyle(tint: .cyan))
                .onChange(of: isLiveMode) { _, newValue in
                    if newValue {
                        audioManager.requestPermission()
                        audioManager.startRecording()
                    } else {
                        audioManager.stopRecording()
                    }
                }
            
            if isLiveMode {
                Text("⚠️ Audio processing active - parameters will update in real-time")
                    .font(.caption)
                    .foregroundColor(.yellow)
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    // MARK: - Category Picker Section
    
    private var categoryPickerSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Parameter Category")
                .font(.headline)
                .foregroundColor(.white)
            
            Picker("Category", selection: $selectedCategory) {
                ForEach(ParameterCategory.allCases, id: \.self) { category in
                    Text(category.rawValue).tag(category)
                }
            }
            .pickerStyle(SegmentedPickerStyle())
        }
    }
    
    // MARK: - Parameter Controls Section
    
    private var parameterControlsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("\(selectedCategory.rawValue) Parameters")
                .font(.headline)
                .foregroundColor(.white)
            
            switch selectedCategory {
            case .frequency:
                frequencyParametersView
            case .correlation:
                correlationParametersView
            case .audio:
                audioParametersView
            case .performance:
                performanceParametersView
            case .advanced:
                advancedParametersView
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    // MARK: - Quick Actions Section
    
    private var quickActionsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Quick Actions")
                .font(.headline)
                .foregroundColor(.white)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                quickActionButton("Paper Spec", icon: "doc.text") {
                    resetToPaperSpec()
                }
                
                quickActionButton("Real-time", icon: "clock") {
                    applyPreset(.realTimeFeedback)
                }
                
                quickActionButton("High Accuracy", icon: "target") {
                    applyPreset(.highAccuracy)
                }
                
                quickActionButton("Research", icon: "flask") {
                    applyPreset(.research)
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    // MARK: - Live Feedback Section
    
    private var liveFeedbackSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Live Audio Feedback")
                .font(.headline)
                .foregroundColor(.white)
            
            HStack {
                VStack(alignment: .leading) {
                    Text("Frequency")
                        .font(.caption)
                        .foregroundColor(.gray)
                    Text("\(audioManager.currentFrequency, specifier: "%.2f") Hz")
                        .font(.title3)
                        .fontWeight(.semibold)
                        .foregroundColor(.cyan)
                }
                
                Spacer()
                
                VStack(alignment: .trailing) {
                    Text("Pressure")
                        .font(.caption)
                        .foregroundColor(.gray)
                    Text("\(audioManager.currentPressure, specifier: "%.1f") cmH₂O")
                        .font(.title3)
                        .fontWeight(.semibold)
                        .foregroundColor(.green)
                }
            }
            
            HStack {
                VStack(alignment: .leading) {
                    Text("Audio Level")
                        .font(.caption)
                        .foregroundColor(.gray)
                    Text("\(audioManager.audioLevel, specifier: "%.4f")")
                        .font(.title3)
                        .fontWeight(.semibold)
                        .foregroundColor(.orange)
                }
                
                Spacer()
                
                VStack(alignment: .trailing) {
                    Text("Breaths")
                        .font(.caption)
                        .foregroundColor(.gray)
                    Text("\(audioManager.breathDetector.breathCount)")
                        .font(.title3)
                        .fontWeight(.semibold)
                        .foregroundColor(.purple)
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    // MARK: - Helper Methods
    
    private func quickActionButton(_ title: String, icon: String, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(.cyan)
                
                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 12)
            .background(Color.gray.opacity(0.2))
            .cornerRadius(8)
        }
    }
    
    private func resetToPaperSpec() {
        configManager.applyPreset(.standard)
        updateAudioManagerIfNeeded()
    }
    
    private func applyPreset(_ preset: ConfigurationPreset) {
        configManager.applyPreset(preset)
        updateAudioManagerIfNeeded()
    }
    
    private func updateAudioManagerIfNeeded() {
        if isLiveMode {
            // Update audio manager with new configuration in real-time
            audioManager.updateConfiguration(configManager.currentConfiguration)
        }
    }
}

// MARK: - Parameter Views

extension DeveloperSettingsView {
    private var frequencyParametersView: some View {
        VStack(spacing: 16) {
            ParameterGroupView(title: "Frequency Detection Range") {
                IntParameterSliderView(
                    title: "Min Frequency",
                    description: "Minimum detectable frequency. Paper spec: 10 Hz. Lower values may increase false positives but improve low-pressure detection.",
                    value: Binding(
                        get: { configManager.currentConfiguration.minFreq },
                        set: { configManager.currentConfiguration.minFreq = $0; updateConfiguration() }
                    ),
                    range: 5...15,
                    unit: " Hz"
                ) { _ in updateConfiguration() }

                IntParameterSliderView(
                    title: "Max Frequency",
                    description: "Maximum detectable frequency. Paper spec: 40 Hz. Higher values may increase noise sensitivity.",
                    value: Binding(
                        get: { configManager.currentConfiguration.maxFreq },
                        set: { configManager.currentConfiguration.maxFreq = $0; updateConfiguration() }
                    ),
                    range: 30...60,
                    unit: " Hz"
                ) { _ in updateConfiguration() }
            }

            ParameterGroupView(title: "Frequency Accuracy") {
                ParameterSliderView(
                    title: "Frequency Accuracy",
                    description: "Frequency detection accuracy. Paper spec: 0.025. Lower values provide higher precision but may reduce stability.",
                    value: Binding(
                        get: { configManager.currentConfiguration.freqAccuracy },
                        set: { configManager.currentConfiguration.freqAccuracy = $0; updateConfiguration() }
                    ),
                    range: 0.01...0.1,
                    step: 0.005,
                    formatSpecifier: "%.3f"
                ) { _ in updateConfiguration() }

                IntParameterSliderView(
                    title: "Lower Formant Frequency",
                    description: "Lower formant frequency for filtering. Paper spec: 250 Hz. Affects voice/breath separation.",
                    value: Binding(
                        get: { configManager.currentConfiguration.lowerFormantFreq },
                        set: { configManager.currentConfiguration.lowerFormantFreq = $0; updateConfiguration() }
                    ),
                    range: 200...400,
                    unit: " Hz"
                ) { _ in updateConfiguration() }
            }
        }
    }

    private var correlationParametersView: some View {
        VStack(spacing: 16) {
            ParameterGroupView(title: "Autocorrelation Settings") {
                ParameterSliderView(
                    title: "Correlation Threshold",
                    description: "Minimum correlation value to accept a pitch detection. Higher values reduce false positives but may miss weak signals.",
                    value: Binding(
                        get: { configManager.currentConfiguration.correlationThreshold },
                        set: { configManager.currentConfiguration.correlationThreshold = $0; updateConfiguration() }
                    ),
                    range: 0.3...0.9,
                    step: 0.05,
                    formatSpecifier: "%.2f"
                ) { _ in updateConfiguration() }

                IntParameterSliderView(
                    title: "Coarse Step",
                    description: "Step size for coarse autocorrelation search. Larger values are faster but less accurate.",
                    value: Binding(
                        get: { configManager.currentConfiguration.coarseStep },
                        set: { configManager.currentConfiguration.coarseStep = $0; updateConfiguration() }
                    ),
                    range: 1...5
                ) { _ in updateConfiguration() }

                IntParameterSliderView(
                    title: "Fine Search Window",
                    description: "Window size for fine autocorrelation search. Larger values are more accurate but slower.",
                    value: Binding(
                        get: { configManager.currentConfiguration.fineSearchWindow },
                        set: { configManager.currentConfiguration.fineSearchWindow = $0; updateConfiguration() }
                    ),
                    range: 3...10
                ) { _ in updateConfiguration() }
            }

            ParameterGroupView(title: "Tracking Parameters") {
                ParameterSliderView(
                    title: "Decay Rate",
                    description: "Rate at which previous detections decay. Paper spec: 0.8. Higher values provide more stability.",
                    value: Binding(
                        get: { configManager.currentConfiguration.decayRate },
                        set: { configManager.currentConfiguration.decayRate = $0; updateConfiguration() }
                    ),
                    range: 0.5...0.95,
                    step: 0.05,
                    formatSpecifier: "%.2f"
                ) { _ in updateConfiguration() }

                ParameterSliderView(
                    title: "Leap Threshold",
                    description: "Maximum allowed frequency jump between detections. Prevents erratic frequency changes.",
                    value: Binding(
                        get: { configManager.currentConfiguration.leapThreshold },
                        set: { configManager.currentConfiguration.leapThreshold = $0; updateConfiguration() }
                    ),
                    range: 0.1...0.5,
                    step: 0.05,
                    formatSpecifier: "%.2f"
                ) { _ in updateConfiguration() }
            }
        }
    }

    private var audioParametersView: some View {
        VStack(spacing: 16) {
            ParameterGroupView(title: "Audio Processing") {
                ParameterSliderView(
                    title: "Sample Rate",
                    description: "Audio sampling frequency. Paper spec: 44,100 Hz. Higher values provide better accuracy but use more CPU.",
                    value: Binding(
                        get: { configManager.currentConfiguration.sampleRate },
                        set: { configManager.currentConfiguration.sampleRate = $0; updateConfiguration() }
                    ),
                    range: 22050...48000,
                    step: 100,
                    unit: " Hz",
                    formatSpecifier: "%.0f"
                ) { _ in updateConfiguration() }

                ParameterSliderView(
                    title: "Buffer Size",
                    description: "Audio buffer size in seconds. Paper spec: 0.1s. Smaller values provide faster updates but less accuracy.",
                    value: Binding(
                        get: { configManager.currentConfiguration.bufferSize },
                        set: { configManager.currentConfiguration.bufferSize = $0; updateConfiguration() }
                    ),
                    range: 0.05...0.5,
                    step: 0.01,
                    unit: " s",
                    formatSpecifier: "%.2f"
                ) { _ in updateConfiguration() }

                ParameterSliderView(
                    title: "Minimum Amplitude",
                    description: "Minimum signal amplitude to process. Paper spec: 2.0e-4. Higher values reduce noise but may miss weak signals.",
                    value: Binding(
                        get: { configManager.currentConfiguration.minAmp },
                        set: { configManager.currentConfiguration.minAmp = $0; updateConfiguration() }
                    ),
                    range: 1.0e-5...1.0e-3,
                    step: 1.0e-5,
                    formatSpecifier: "%.1e"
                ) { _ in updateConfiguration() }
            }
        }
    }

    private var performanceParametersView: some View {
        VStack(spacing: 16) {
            ParameterGroupView(title: "Performance Optimization") {
                IntParameterSliderView(
                    title: "Downsample Factor",
                    description: "Factor to downsample audio for processing. Higher values improve performance but reduce accuracy.",
                    value: Binding(
                        get: { configManager.currentConfiguration.downsampleFactorOverride ?? 35 },
                        set: { configManager.currentConfiguration.downsampleFactorOverride = $0; updateConfiguration() }
                    ),
                    range: 20...60
                ) { _ in updateConfiguration() }

                IntParameterSliderView(
                    title: "Target Buffer Length",
                    description: "Target length for internal processing buffer. Affects memory usage and processing latency.",
                    value: Binding(
                        get: { configManager.currentConfiguration.targetBufferLength },
                        set: { configManager.currentConfiguration.targetBufferLength = $0; updateConfiguration() }
                    ),
                    range: 100...1000
                ) { _ in updateConfiguration() }

                IntParameterSliderView(
                    title: "Min Data Check",
                    description: "Minimum number of samples required before processing. Prevents processing of insufficient data.",
                    value: Binding(
                        get: { configManager.currentConfiguration.minDataCheck },
                        set: { configManager.currentConfiguration.minDataCheck = $0; updateConfiguration() }
                    ),
                    range: 50...200
                ) { _ in updateConfiguration() }
            }

            ParameterGroupView(title: "Algorithm Behavior") {
                IntParameterSliderView(
                    title: "Max Run Length",
                    description: "Maximum consecutive detections before requiring validation. Prevents stuck frequencies.",
                    value: Binding(
                        get: { configManager.currentConfiguration.maxRunLength },
                        set: { configManager.currentConfiguration.maxRunLength = $0; updateConfiguration() }
                    ),
                    range: 3...10
                ) { _ in updateConfiguration() }

                BoolParameterToggleView(
                    title: "Save Results",
                    description: "Enable saving of detection results for analysis. May impact performance.",
                    value: Binding(
                        get: { configManager.currentConfiguration.saveResults },
                        set: { configManager.currentConfiguration.saveResults = $0; updateConfiguration() }
                    )
                ) { _ in updateConfiguration() }
            }
        }
    }

    private var advancedParametersView: some View {
        VStack(spacing: 16) {
            ParameterGroupView(title: "Advanced Settings") {
                BoolParameterToggleView(
                    title: "Save Results",
                    description: "Enable saving of detection results for debugging and research purposes.",
                    value: Binding(
                        get: { configManager.currentConfiguration.saveResults },
                        set: { configManager.currentConfiguration.saveResults = $0; updateConfiguration() }
                    )
                ) { _ in updateConfiguration() }

                IntParameterSliderView(
                    title: "Smoothing Filter Size",
                    description: "Override for smoothing filter size. If not set, calculated automatically from sample rate and formant frequency.",
                    value: Binding(
                        get: { configManager.currentConfiguration.smoothingFilterSizeOverride ?? Int(configManager.currentConfiguration.sampleRate / Float(configManager.currentConfiguration.lowerFormantFreq)) },
                        set: { configManager.currentConfiguration.smoothingFilterSizeOverride = $0; updateConfiguration() }
                    ),
                    range: 50...500
                ) { _ in updateConfiguration() }

                ParameterSliderView(
                    title: "Gaussian Sigma",
                    description: "Override for Gaussian sigma parameter. If not set, calculated automatically from downsample factor and frequency range.",
                    value: Binding(
                        get: { configManager.currentConfiguration.gaussianSigmaOverride ?? (0.2 * Float(configManager.currentConfiguration.downsampleFactor) * Float(configManager.currentConfiguration.maxFreq) / configManager.currentConfiguration.sampleRate) },
                        set: { configManager.currentConfiguration.gaussianSigmaOverride = $0; updateConfiguration() }
                    ),
                    range: 0.001...0.1,
                    step: 0.001,
                    formatSpecifier: "%.3f"
                ) { _ in updateConfiguration() }
            }

            ParameterGroupView(title: "Pressure Calculation") {
                ParameterSliderView(
                    title: "Pressure Slope",
                    description: "Slope coefficient for pressure calculation from frequency. Calibrated value: 1.119",
                    value: Binding(
                        get: { configManager.currentConfiguration.pressureSlope },
                        set: { configManager.currentConfiguration.pressureSlope = $0; updateConfiguration() }
                    ),
                    range: 0.5...2.0,
                    step: 0.01,
                    formatSpecifier: "%.3f"
                ) { _ in updateConfiguration() }

                ParameterSliderView(
                    title: "Pressure Intercept",
                    description: "Intercept coefficient for pressure calculation from frequency. Calibrated value: -4.659",
                    value: Binding(
                        get: { configManager.currentConfiguration.pressureIntercept },
                        set: { configManager.currentConfiguration.pressureIntercept = $0; updateConfiguration() }
                    ),
                    range: -10.0...0.0,
                    step: 0.01,
                    formatSpecifier: "%.3f"
                ) { _ in updateConfiguration() }
            }

            ParameterGroupView(title: "Validation Ranges") {
                ParameterSliderView(
                    title: "Min Valid Frequency",
                    description: "Minimum frequency considered valid for pressure calculation.",
                    value: Binding(
                        get: { configManager.currentConfiguration.minValidFreq },
                        set: { configManager.currentConfiguration.minValidFreq = $0; updateConfiguration() }
                    ),
                    range: 5.0...15.0,
                    step: 0.5,
                    unit: " Hz",
                    formatSpecifier: "%.1f"
                ) { _ in updateConfiguration() }

                ParameterSliderView(
                    title: "Max Valid Pressure",
                    description: "Maximum pressure considered valid. Values above this are clamped.",
                    value: Binding(
                        get: { configManager.currentConfiguration.maxValidPressure },
                        set: { configManager.currentConfiguration.maxValidPressure = $0; updateConfiguration() }
                    ),
                    range: 25.0...50.0,
                    step: 1.0,
                    unit: " cmH₂O",
                    formatSpecifier: "%.0f"
                ) { _ in updateConfiguration() }
            }
        }
    }

    private func updateConfiguration() {
        configManager.saveConfiguration()
        updateAudioManagerIfNeeded()
    }
}

#endif

#Preview {
    #if DEBUG
    DeveloperSettingsView()
    #else
    Text("Developer Settings only available in DEBUG builds")
    #endif
}
