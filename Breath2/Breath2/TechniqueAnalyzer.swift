//
//  TechniqueAnalyzer.swift
//  Breath2
//
//  Created by <PERSON> on 02/06/2025.
//
//  Analyzes exhalation technique and provides qualitative feedback zones
//  Based on device sound characteristics rather than physiological measurements
//

import Foundation
import SwiftUI

// MARK: - Exhalation Zone Enum

enum ExhalationZone: String, CaseIterable, Codable {
    case noEffort = "No Effort"
    case lowEffort = "Low Effort" 
    case goodTechnique = "Good Technique"
    case highEffort = "High Effort"
    
    var color: Color {
        switch self {
        case .noEffort: return .gray
        case .lowEffort: return .orange
        case .goodTechnique: return .green
        case .highEffort: return .red
        }
    }
    
    var icon: String {
        switch self {
        case .noEffort: return "pause.circle"
        case .lowEffort: return "arrow.up.circle"
        case .goodTechnique: return "checkmark.circle"
        case .highEffort: return "arrow.down.circle"
        }
    }
    
    var description: String {
        switch self {
        case .noEffort: return "Device is quiet"
        case .lowEffort: return "Device flutter is low"
        case .goodTechnique: return "Device flutter is steady"
        case .highEffort: return "Device flutter is intense"
        }
    }
    
    var guidanceText: String {
        switch self {
        case .noEffort: return "Begin your exhalation"
        case .lowEffort: return "Device sound suggests gentle increase"
        case .goodTechnique: return "Device sound is in optimal range"
        case .highEffort: return "Device sound suggests gentle decrease"
        }
    }
}

class TechniqueAnalyzer {
    
    // MARK: - Internal Model Parameters
    // These remain as implementation details for zone classification
    private let slope: Float = 1.119
    private let intercept: Float = -4.659
    
    // MARK: - Validation Parameters
    private let minValidFreq: Float = 7.0  // Hz - minimum detectable frequency
    private let maxValidFreq: Float = 40.0  // Hz - maximum detectable frequency
    
    // MARK: - Zone Thresholds (Internal Implementation Detail)
    private let lowEffortThreshold: Float = 10.0
    private let highEffortThreshold: Float = 20.0
    
    // MARK: - Initialization
    init() {
        print("TechniqueAnalyzer initialized:")
        print("  Analyzing device sound patterns for technique guidance")
        print("  Valid frequency range: \(minValidFreq)-\(maxValidFreq) Hz")
        print("  Providing qualitative zone feedback")
    }
    
    // MARK: - Main Classification Method
    
    /// Classifies pitch into qualitative exhalation zones
    /// - Parameter pitch: Detected frequency in Hz
    /// - Returns: Qualitative zone representing technique level
    func classifyPitch(_ pitch: Float) -> ExhalationZone {
        // Validate input frequency
        guard pitch >= minValidFreq && pitch <= maxValidFreq else {
            return .noEffort
        }
        
        // Internal calculation for zone determination (implementation detail)
        let internalValue = slope * pitch + intercept
        
        // Map internal value to qualitative zones
        if internalValue < lowEffortThreshold {
            return .lowEffort
        } else if internalValue <= highEffortThreshold {
            return .goodTechnique
        } else {
            return .highEffort
        }
    }
    
    // MARK: - Utility Methods
    
    /// Checks if the detected frequency is within the valid range
    /// - Parameter frequency: Frequency in Hz
    /// - Returns: True if frequency is valid for analysis
    func isValidFrequency(_ frequency: Float) -> Bool {
        return frequency >= minValidFreq && frequency <= maxValidFreq
    }
    
    /// Checks if the technique is in the optimal zone
    /// - Parameter zone: Current exhalation zone
    /// - Returns: True if technique is optimal
    func isOptimalTechnique(_ zone: ExhalationZone) -> Bool {
        return zone == .goodTechnique
    }
    
    /// Calculates the confidence level of the technique reading
    /// Based on how well the frequency fits within the expected range
    /// - Parameter frequency: Detected frequency in Hz
    /// - Returns: Confidence level from 0.0 to 1.0
    func getConfidenceLevel(_ frequency: Float) -> Float {
        guard isValidFrequency(frequency) else {
            return 0.0
        }
        
        // Higher confidence for frequencies in the middle of the range
        let midFreq = (minValidFreq + maxValidFreq) / 2.0
        let freqRange = maxValidFreq - minValidFreq
        let distanceFromMid = abs(frequency - midFreq)
        let normalizedDistance = distanceFromMid / (freqRange / 2.0)
        
        // Confidence decreases as we move away from the center frequency
        return max(0.0, 1.0 - normalizedDistance)
    }
    
    /// Provides information about the analyzer
    /// - Returns: Dictionary with analyzer information
    func getAnalyzerInfo() -> [String: Any] {
        return [
            "frequency_range": "\(minValidFreq)-\(maxValidFreq) Hz",
            "zones": ExhalationZone.allCases.map { $0.rawValue },
            "optimal_zone": ExhalationZone.goodTechnique.rawValue,
            "analysis_type": "Qualitative technique guidance"
        ]
    }
}
