//
//  TabBarView.swift
//  Breath2
//
//  Created by <PERSON> on 02/06/2025.
//

import SwiftUI
import Charts

struct TabBarView: View {
    @State private var selectedTab = 1 // Start with PEP Therapy tab
    @StateObject private var historyManager = SessionHistoryManager()
    @StateObject private var therapyConfiguration = TherapyConfiguration()

    var body: some View {
        TabView(selection: $selectedTab) {
            // History Tab
            HistoryView()
                .environmentObject(historyManager)
                .environmentObject(therapyConfiguration)
                .tabItem {
                    Image(systemName: "chart.line.uptrend.xyaxis")
                    Text("History")
                }
                .tag(0)

            // PEP Therapy Tab (Main)
            TherapyView(configuration: therapyConfiguration)
                .environmentObject(historyManager)
                .tabItem {
                    Image(systemName: "lungs.fill")
                    Text("PEP Therapy")
                }
                .tag(1)

            // Settings Tab
            SettingsView(configuration: therapyConfiguration)
                .tabItem {
                    Image(systemName: "gearshape.fill")
                    Text("Settings")
                }
                .tag(2)
            
            // Profile Tab
            ProfileView()
                .tabItem {
                    Image(systemName: "person.fill")
                    Text("Profile")
                }
                .tag(3)

            #if DEBUG
            // Developer Settings Tab (DEBUG only)
            DeveloperSettingsView()
                .tabItem {
                    Image(systemName: "wrench.and.screwdriver.fill")
                    Text("Dev")
                }
                .tag(4)
            #endif
        }
        .accentColor(.cyan)
        .onAppear {
            // Customize tab bar appearance for dark theme
            let appearance = UITabBarAppearance()
            appearance.configureWithOpaqueBackground()
            appearance.backgroundColor = UIColor(red: 0.05, green: 0.05, blue: 0.08, alpha: 1.0)

            // Set shadow and styling
            appearance.shadowColor = UIColor.black.withAlphaComponent(0.3)
            appearance.shadowImage = UIImage()

            // Customize tab item colors
            appearance.stackedLayoutAppearance.normal.iconColor = UIColor.gray
            appearance.stackedLayoutAppearance.normal.titleTextAttributes = [.foregroundColor: UIColor.gray]
            appearance.stackedLayoutAppearance.selected.iconColor = UIColor.cyan
            appearance.stackedLayoutAppearance.selected.titleTextAttributes = [.foregroundColor: UIColor.cyan]

            UITabBar.appearance().standardAppearance = appearance
            UITabBar.appearance().scrollEdgeAppearance = appearance
        }
    }
}

// MARK: - Placeholder Views

struct HistoryView: View {
    @EnvironmentObject var historyManager: SessionHistoryManager
    @EnvironmentObject var therapyConfiguration: TherapyConfiguration
    @State private var selectedTimeframe: TimeFrame = .week
    @State private var selectedSession: CompletedSession?
    @State private var showingSessionDetail = false
    @State private var expandedDates: Set<String> = []

    enum TimeFrame: String, CaseIterable {
        case day = "Today"
        case week = "This Week"
        case month = "This Month"
        case year = "This Year"

        var systemImage: String {
            switch self {
            case .day: return "calendar"
            case .week: return "calendar.badge.clock"
            case .month: return "calendar.badge.plus"
            case .year: return "calendar.badge.exclamationmark"
            }
        }
    }

    var filteredSessions: [CompletedSession] {
        let calendar = Calendar.current
        let now = Date()

        return historyManager.sessions.filter { session in
            switch selectedTimeframe {
            case .day:
                return calendar.isDate(session.date, inSameDayAs: now)
            case .week:
                return calendar.isDate(session.date, equalTo: now, toGranularity: .weekOfYear)
            case .month:
                return calendar.isDate(session.date, equalTo: now, toGranularity: .month)
            case .year:
                return calendar.isDate(session.date, equalTo: now, toGranularity: .year)
            }
        }
    }

    var body: some View {
        NavigationView {
            ZStack {
                // Professional dark background to match main app
                LinearGradient(
                    colors: [
                        Color(red: 0.05, green: 0.05, blue: 0.08),
                        Color(red: 0.08, green: 0.08, blue: 0.12),
                        Color(red: 0.10, green: 0.10, blue: 0.15)
                    ],
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea()

                if historyManager.sessions.isEmpty {
                    emptyStateView
                } else {
                    ScrollView(showsIndicators: false) {
                        VStack(spacing: 24) {
                            // Adherence Summary Section
                            adherenceSummaryView

                            // Time Filter
                            timeFilterView

                            // Sessions List
                            sessionsListView

                            Spacer(minLength: 100) // Space for tab bar
                        }
                        .padding(.horizontal, 16)
                        .padding(.top, 10)
                    }
                }
            }
            .navigationTitle("History")
            .navigationBarTitleDisplayMode(.large)
            .toolbarBackground(Color(red: 0.05, green: 0.05, blue: 0.08), for: .navigationBar)
            .toolbarColorScheme(.dark, for: .navigationBar)
            .sheet(isPresented: $showingSessionDetail) {
                if let session = selectedSession {
                    SessionDetailView(session: session)
                }
            }
        }
    }

    // MARK: - Empty State View

    private var emptyStateView: some View {
        VStack(spacing: 24) {
            // Animated breathing icon
            Image(systemName: "lungs.fill")
                .font(.system(size: 80))
                .foregroundStyle(
                    LinearGradient(
                        colors: [.cyan, .blue],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .scaleEffect(1.0)
                .animation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true), value: UUID())

            VStack(spacing: 12) {
                Text("No Sessions Yet")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)

                Text("Complete your first therapy session to see your progress here")
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.7))
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 32)
            }
        }
    }

    // MARK: - Adherence Summary View

    private var adherenceSummaryView: some View {
        VStack(spacing: 20) {
            // Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Adherence Overview")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)

                    Text(selectedTimeframe.rawValue)
                        .font(.subheadline)
                        .foregroundColor(.white.opacity(0.7))
                }

                Spacer()

                // Overall adherence score
                VStack(spacing: 4) {
                    Text("\(Int(adherenceScore * 100))%")
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(adherenceColor)

                    Text("Adherence")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.7))
                }
            }

            // Adherence metrics
            HStack(spacing: 12) {
                // Sessions Completed vs Planned
                AdherenceCard(
                    title: "Sessions",
                    value: "\(filteredSessions.count)",
                    subtitle: "of \(plannedSessions) planned",
                    icon: "calendar.badge.checkmark",
                    color: sessionsAdherenceColor,
                    progress: sessionsAdherenceProgress
                )

                // Average Quality
                AdherenceCard(
                    title: "Quality",
                    value: averageQualityText,
                    subtitle: "avg session quality",
                    icon: "star.fill",
                    color: qualityColor,
                    progress: averageQualityProgress
                )
            }

            // Compliance trend chart
            if !filteredSessions.isEmpty {
                complianceTrendView
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(adherenceColor.opacity(0.3), lineWidth: 1)
                )
        )
    }

    // MARK: - Time Filter View

    private var timeFilterView: some View {
        HStack(spacing: 0) {
            ForEach(TimeFrame.allCases, id: \.self) { timeframe in
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.2)) {
                        selectedTimeframe = timeframe
                        expandedDates.removeAll() // Collapse all when changing timeframe
                    }
                }) {
                    HStack(spacing: 6) {
                        Image(systemName: timeframe.systemImage)
                            .font(.system(size: 12, weight: .medium))

                        Text(timeframe.rawValue)
                            .font(.system(size: 14, weight: .medium, design: .rounded))
                    }
                    .foregroundColor(selectedTimeframe == timeframe ? .white : .white.opacity(0.7))
                    .padding(.vertical, 10)
                    .padding(.horizontal, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(selectedTimeframe == timeframe ? Color.cyan : Color.clear)
                    )
                }
            }
        }
        .padding(4)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.05))
        )
    }

    // MARK: - Sessions List View

    private var sessionsListView: some View {
        VStack(alignment: .leading, spacing: 16) {
            if !filteredSessions.isEmpty {
                HStack {
                    Text("Sessions")
                        .font(.title3)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)

                    Spacer()

                    Text("\(filteredSessions.count) sessions")
                        .font(.subheadline)
                        .foregroundColor(.white.opacity(0.7))
                }

                LazyVStack(spacing: 12) {
                    ForEach(groupedSessions, id: \.key) { dateGroup in
                        CollapsibleSessionGroupView(
                            date: dateGroup.key,
                            sessions: dateGroup.value,
                            isExpanded: expandedDates.contains(dateKey(for: dateGroup.key)),
                            onToggleExpansion: {
                                let key = dateKey(for: dateGroup.key)
                                if expandedDates.contains(key) {
                                    expandedDates.remove(key)
                                } else {
                                    expandedDates.insert(key)
                                }
                            },
                            onSessionTap: { session in
                                selectedSession = session
                                showingSessionDetail = true
                            }
                        )
                    }
                }
            }
        }
    }

    // MARK: - Computed Properties for Adherence

    private var plannedSessions: Int {
        switch selectedTimeframe {
        case .day:
            return therapyConfiguration.dailyBlocks.count
        case .week:
            return therapyConfiguration.dailyBlocks.count * 7
        case .month:
            return therapyConfiguration.dailyBlocks.count * 30
        case .year:
            return therapyConfiguration.dailyBlocks.count * 365
        }
    }

    private var adherenceScore: Double {
        guard plannedSessions > 0 else { return 0 }
        return min(Double(filteredSessions.count) / Double(plannedSessions), 1.0)
    }

    private var adherenceColor: Color {
        if adherenceScore >= 0.8 { return .green }
        else if adherenceScore >= 0.6 { return .yellow }
        else if adherenceScore >= 0.4 { return .orange }
        else { return .red }
    }

    private var sessionsAdherenceProgress: Double {
        guard plannedSessions > 0 else { return 0 }
        return min(Double(filteredSessions.count) / Double(plannedSessions), 1.0)
    }

    private var sessionsAdherenceColor: Color {
        if sessionsAdherenceProgress >= 0.8 { return .green }
        else if sessionsAdherenceProgress >= 0.6 { return .yellow }
        else { return .orange }
    }

    private var averageQualityScore: Double {
        guard !filteredSessions.isEmpty else { return 0 }
        let totalScore = filteredSessions.reduce(0.0) { sum, session in
            switch session.quality {
            case .excellent: return sum + 1.0
            case .good: return sum + 0.8
            case .fair: return sum + 0.6
            case .poor: return sum + 0.4
            case .none: return sum + 0.0
            }
        }
        return totalScore / Double(filteredSessions.count)
    }

    private var averageQualityText: String {
        if averageQualityScore >= 0.9 { return "Excellent" }
        else if averageQualityScore >= 0.7 { return "Good" }
        else if averageQualityScore >= 0.5 { return "Fair" }
        else { return "Poor" }
    }

    private var averageQualityProgress: Double {
        return averageQualityScore
    }

    private var qualityColor: Color {
        if averageQualityScore >= 0.8 { return .green }
        else if averageQualityScore >= 0.6 { return .yellow }
        else { return .orange }
    }

    private var groupedSessions: [(key: Date, value: [CompletedSession])] {
        let calendar = Calendar.current
        let grouped = Dictionary(grouping: filteredSessions) { session in
            calendar.startOfDay(for: session.date)
        }
        return grouped.sorted { $0.key > $1.key }
    }

    // MARK: - Helper Functions

    private func formatDuration(_ duration: TimeInterval) -> String {
        let hours = Int(duration) / 3600
        let minutes = Int(duration) % 3600 / 60

        if hours > 0 {
            return "\(hours)h \(minutes)m"
        } else {
            return "\(minutes)m"
        }
    }

    private func dateKey(for date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter.string(from: date)
    }

    // MARK: - Compliance Trend View

    private var complianceTrendView: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Compliance Trend")
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(.white)

            HStack(spacing: 8) {
                ForEach(Array(filteredSessions.prefix(7).enumerated()), id: \.offset) { index, session in
                    VStack(spacing: 4) {
                        RoundedRectangle(cornerRadius: 4)
                            .fill(session.qualityColor)
                            .frame(width: 24, height: CGFloat(20 + session.completionPercentage * 30))

                        Text(session.date, format: .dateTime.weekday(.abbreviated))
                            .font(.caption2)
                            .foregroundColor(.white.opacity(0.7))
                    }
                }

                if filteredSessions.count < 7 {
                    ForEach(0..<(7 - filteredSessions.count), id: \.self) { _ in
                        VStack(spacing: 4) {
                            RoundedRectangle(cornerRadius: 4)
                                .fill(Color.gray.opacity(0.3))
                                .frame(width: 24, height: 20)

                            Text("--")
                                .font(.caption2)
                                .foregroundColor(.white.opacity(0.7))
                        }
                    }
                }
            }
        }
        .padding(.top, 8)
    }
}

// MARK: - Supporting Views

struct AdherenceCard: View {
    let title: String
    let value: String
    let subtitle: String
    let icon: String
    let color: Color
    let progress: Double

    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: icon)
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(color)

                Spacer()

                Text(value)
                    .font(.system(size: 18, weight: .bold, design: .rounded))
                    .foregroundColor(.white)
            }

            VStack(alignment: .leading, spacing: 6) {
                HStack {
                    Text(title)
                        .font(.system(size: 14, weight: .semibold, design: .rounded))
                        .foregroundColor(.white)

                    Spacer()
                }

                Text(subtitle)
                    .font(.system(size: 11, weight: .medium, design: .rounded))
                    .foregroundColor(.white.opacity(0.7))

                // Progress bar
                GeometryReader { geometry in
                    ZStack(alignment: .leading) {
                        RoundedRectangle(cornerRadius: 2)
                            .fill(Color.gray.opacity(0.3))
                            .frame(height: 4)

                        RoundedRectangle(cornerRadius: 2)
                            .fill(color)
                            .frame(width: geometry.size.width * progress, height: 4)
                    }
                }
                .frame(height: 4)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(color.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

struct StatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color

    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.system(size: 24, weight: .semibold))
                .foregroundColor(color)

            Text(value)
                .font(.system(size: 18, weight: .bold, design: .rounded))
                .foregroundColor(.white)

            Text(title)
                .font(.system(size: 12, weight: .medium, design: .rounded))
                .foregroundColor(.white.opacity(0.7))
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(color.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

struct CollapsibleSessionGroupView: View {
    let date: Date
    let sessions: [CompletedSession]
    let isExpanded: Bool
    let onToggleExpansion: () -> Void
    let onSessionTap: (CompletedSession) -> Void

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Date header with expand/collapse button
            Button(action: {
                withAnimation(.easeInOut(duration: 0.3)) {
                    onToggleExpansion()
                }
            }) {
                HStack {
                    HStack(spacing: 8) {
                        Image(systemName: isExpanded ? "chevron.down" : "chevron.right")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(.cyan)
                            .animation(.easeInOut(duration: 0.2), value: isExpanded)

                        Text(date, format: .dateTime.weekday(.wide).month().day())
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.white)
                    }

                    Spacer()

                    HStack(spacing: 8) {
                        Text("\(sessions.count) session\(sessions.count == 1 ? "" : "s")")
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.7))

                        // Quality indicator dots
                        HStack(spacing: 2) {
                            ForEach(sessions.prefix(3), id: \.id) { session in
                                Circle()
                                    .fill(session.qualityColor)
                                    .frame(width: 6, height: 6)
                            }
                            if sessions.count > 3 {
                                Text("+\(sessions.count - 3)")
                                    .font(.caption2)
                                    .foregroundColor(.white.opacity(0.7))
                            }
                        }
                    }
                }
                .padding(.vertical, 8)
                .padding(.horizontal, 12)
                .background(
                    RoundedRectangle(cornerRadius: 10)
                        .fill(Color.white.opacity(0.05))
                        .overlay(
                            RoundedRectangle(cornerRadius: 10)
                                .stroke(Color.cyan.opacity(0.2), lineWidth: 1)
                        )
                )
            }
            .buttonStyle(PlainButtonStyle())

            // Sessions for this date (collapsible)
            if isExpanded {
                VStack(spacing: 8) {
                    ForEach(sessions) { session in
                        SessionCard(session: session, onTap: {
                            onSessionTap(session)
                        })
                    }
                }
                .transition(.asymmetric(
                    insertion: .opacity.combined(with: .move(edge: .top)),
                    removal: .opacity.combined(with: .move(edge: .top))
                ))
            }
        }
    }
}

struct SessionCard: View {
    let session: CompletedSession
    let onTap: (() -> Void)?

    init(session: CompletedSession, onTap: (() -> Void)? = nil) {
        self.session = session
        self.onTap = onTap
    }

    var body: some View {
        Button(action: {
            onTap?()
        }) {
            HStack(spacing: 16) {
                // Quality indicator
                VStack(spacing: 6) {
                    ZStack {
                        Circle()
                            .fill(session.qualityColor.opacity(0.2))
                            .frame(width: 44, height: 44)

                        Image(systemName: session.qualityIcon)
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(session.qualityColor)
                    }

                    Text(session.quality.description)
                        .font(.system(size: 10, weight: .medium, design: .rounded))
                        .foregroundColor(session.qualityColor)
                }
                .frame(width: 60)

                // Session details
                VStack(alignment: .leading, spacing: 6) {
                    HStack {
                        Text(session.date, style: .time)
                            .font(.system(size: 16, weight: .semibold, design: .rounded))
                            .foregroundColor(.white)

                        Spacer()

                        Text(session.formattedDuration)
                            .font(.system(size: 14, weight: .medium, design: .rounded))
                            .foregroundColor(.cyan)
                    }

                    HStack(spacing: 16) {
                        HStack(spacing: 4) {
                            Image(systemName: "checkmark.circle")
                                .font(.caption)
                                .foregroundColor(.white.opacity(0.7))
                            Text("\(session.stepsCompleted)/\(session.totalSteps)")
                                .font(.system(size: 12, weight: .medium, design: .rounded))
                                .foregroundColor(.white.opacity(0.7))
                        }

                        if session.averagePressure > 0 {
                            HStack(spacing: 4) {
                                Image(systemName: "gauge")
                                    .font(.caption)
                                    .foregroundColor(.white.opacity(0.7))
                                Text("\(String(format: "%.1f", session.averagePressure)) cm H₂O")
                                    .font(.system(size: 12, weight: .medium, design: .rounded))
                                    .foregroundColor(.white.opacity(0.7))
                            }
                        }
                    }
                }

                Spacer()

                // Chevron indicator
                Image(systemName: "chevron.right")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white.opacity(0.7))
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.white.opacity(0.05))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(session.qualityColor.opacity(0.2), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// CircularProgressView is defined in CircularProgressView.swift

struct SettingsView: View {
    @ObservedObject var configuration: TherapyConfiguration
    @State private var showingTherapySettings = false

    var body: some View {
        NavigationView {
            ZStack {
                // Professional dark background to match main app
                LinearGradient(
                    colors: [
                        Color(red: 0.05, green: 0.05, blue: 0.08),
                        Color(red: 0.08, green: 0.08, blue: 0.12),
                        Color(red: 0.10, green: 0.10, blue: 0.15)
                    ],
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea()

                ScrollView {
                    VStack(spacing: 24) {
                        // Therapy Session Settings Section
                        therapySessionSettingsSection

                        // Other settings sections can be added here

                        Spacer(minLength: 100) // Space for tab bar
                    }
                    .padding(.horizontal, 16)
                    .padding(.top, 10)
                }
            }
            .navigationTitle("Settings")
            .navigationBarTitleDisplayMode(.large)
            .toolbarBackground(Color(red: 0.05, green: 0.05, blue: 0.08), for: .navigationBar)
            .toolbarColorScheme(.dark, for: .navigationBar)
            .sheet(isPresented: $showingTherapySettings) {
                TherapySettingsView(configuration: configuration)
            }
        }
    }

    private var therapySessionSettingsSection: some View {
        Button(action: {
            showingTherapySettings = true
        }) {
            VStack(alignment: .leading, spacing: 16) {
                // Section header
                HStack {
                    Image(systemName: "lungs.fill")
                        .font(.system(size: 24))
                        .foregroundColor(.blue)

                    VStack(alignment: .leading, spacing: 2) {
                        Text("Therapy Session Settings")
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(.white)

                        Text("Configure your daily therapy routine")
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.7))
                    }

                    Spacer()

                    Image(systemName: "chevron.right")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.blue)
                }
                .padding(.bottom, 8)

                // Current configuration summary
                currentConfigurationSummary
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color.white.opacity(0.05))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(Color.blue.opacity(0.2), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    private var currentConfigurationSummary: some View {
        HStack(spacing: 16) {
            // Three elegant blocks showing session configuration
            HStack(spacing: 8) {
                ForEach(Array(configuration.dailyBlocks.enumerated()), id: \.offset) { index, block in
                    VStack(spacing: 4) {
                        Text("\(block.exhalations)")
                            .font(.title3)
                            .fontWeight(.bold)
                            .foregroundColor(.white)

                        Text(sessionName(for: index))
                            .font(.caption2)
                            .foregroundColor(.white.opacity(0.7))
                    }
                    .frame(width: 50, height: 50)
                    .background(
                        RoundedRectangle(cornerRadius: 10)
                            .fill(Color.blue.opacity(0.2))
                            .overlay(
                                RoundedRectangle(cornerRadius: 10)
                                    .stroke(Color.blue.opacity(0.4), lineWidth: 1)
                            )
                    )
                }
            }

            Spacer()

            // Total and estimated time
            VStack(alignment: .trailing, spacing: 4) {
                Text("\(configuration.totalExhalationsToday)")
                    .font(.title3)
                    .fontWeight(.bold)
                    .foregroundColor(.cyan)

                Text("Total • \(estimatedTime)")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
            }
        }
        .padding()
        .background(Color.gray.opacity(0.08))
        .cornerRadius(12)
    }

    private func sessionName(for index: Int) -> String {
        switch index {
        case 0: return "Morning"
        case 1: return "Afternoon"
        case 2: return "Evening"
        default: return "Session \(index + 1)"
        }
    }

    private var estimatedTime: String {
        let totalMinutes = configuration.dailyBlocks.count * 3 // Rough estimate: 3 minutes per session
        return "\(totalMinutes) min"
    }


}

struct PresetButton: View {
    let title: String
    let subtitle: String
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 4) {
                Text(title)
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(isSelected ? .white : .blue)

                Text(subtitle)
                    .font(.caption)
                    .foregroundColor(isSelected ? .white.opacity(0.8) : .secondary)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .fill(isSelected ? Color.blue : Color.blue.opacity(0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(Color.blue, lineWidth: isSelected ? 0 : 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct ProfileView: View {
    var body: some View {
        NavigationView {
            ZStack {
                // Dark background to match main app
                LinearGradient(
                    colors: [
                        Color(red: 0.05, green: 0.05, blue: 0.08),
                        Color(red: 0.08, green: 0.08, blue: 0.12),
                        Color(red: 0.10, green: 0.10, blue: 0.15)
                    ],
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea()

                VStack {
                    Image(systemName: "person.fill")
                        .font(.system(size: 60))
                        .foregroundColor(.cyan)
                        .padding()

                    Text("Profile")
                        .font(.title2)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)

                    Text("Manage your account and preferences")
                        .font(.subheadline)
                        .foregroundColor(.white.opacity(0.7))
                        .multilineTextAlignment(.center)
                        .padding()
                }
            }
            .navigationTitle("Profile")
            .navigationBarTitleDisplayMode(.large)
            .toolbarBackground(Color(red: 0.05, green: 0.05, blue: 0.08), for: .navigationBar)
            .toolbarColorScheme(.dark, for: .navigationBar)
        }
    }
}

#Preview {
    TabBarView()
}
