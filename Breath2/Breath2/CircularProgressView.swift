//
//  CircularProgressView.swift
//  Breath2
//
//  Created by <PERSON> on 02/06/2025.
//

import SwiftUI

struct CircularProgressView: View {
    let progress: Double
    let value: Double
    let maxValue: Double
    let lineWidth: CGFloat
    let size: CGFloat
    
    @State private var animatedProgress: Double = 0
    @State private var animatedValue: Double = 0
    
    init(
        progress: Double,
        value: Double,
        maxValue: Double = 100,
        lineWidth: CGFloat = 12,
        size: CGFloat = 200
    ) {
        self.progress = progress
        self.value = value
        self.maxValue = maxValue
        self.lineWidth = lineWidth
        self.size = size
    }
    
    var body: some View {
        ZStack {
            // Background circle
            Circle()
                .stroke(
                    Color.gray.opacity(0.2),
                    lineWidth: lineWidth
                )
                .frame(width: size, height: size)
            
            // Progress circle
            Circle()
                .trim(from: 0, to: animatedProgress)
                .stroke(
                    progressGradient,
                    style: StrokeStyle(
                        lineWidth: lineWidth,
                        lineCap: .round
                    )
                )
                .frame(width: size, height: size)
                .rotationEffect(.degrees(-90))
                .animation(.easeInOut(duration: 1.0), value: animatedProgress)
            
            // Center content
            VStack(spacing: 8) {
                // Main value
                Text("\(Int(animatedValue))")
                    .font(.system(size: 48, weight: .bold, design: .rounded))
                    .foregroundColor(.primary)
                    .contentTransition(.numericText())
                
                // Unit or description
                Text("cm H₂O")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
                    .fontWeight(.medium)
            }
        }
        .onAppear {
            withAnimation(.easeInOut(duration: 1.0)) {
                animatedProgress = progress
                animatedValue = value
            }
        }
        .onChange(of: progress) {
            withAnimation(.easeInOut(duration: 0.5)) {
                animatedProgress = progress
            }
        }
        .onChange(of: value) {
            withAnimation(.easeInOut(duration: 0.5)) {
                animatedValue = value
            }
        }
    }
    
    private var progressGradient: AngularGradient {
        let colors = progressColors
        return AngularGradient(
            gradient: Gradient(colors: colors),
            center: .center,
            startAngle: .degrees(0),
            endAngle: .degrees(360 * progress)
        )
    }
    
    private var progressColors: [Color] {
        let normalizedValue = value / maxValue
        
        if normalizedValue < 0.3 {
            // Low pressure - red to orange
            return [.red, .orange]
        } else if normalizedValue < 0.5 {
            // Medium-low pressure - orange to yellow
            return [.orange, .yellow]
        } else if normalizedValue < 0.8 {
            // Good pressure - green
            return [.green, .mint]
        } else {
            // High pressure - blue to purple
            return [.blue, .purple]
        }
    }
}

// MARK: - Animated Circular Progress with Tick Marks

struct DetailedCircularProgressView: View {
    let progress: Double
    let value: Double
    let targetMin: Double
    let targetMax: Double
    let maxValue: Double
    
    @State private var animatedProgress: Double = 0
    @State private var animatedValue: Double = 0
    
    init(
        progress: Double,
        value: Double,
        targetMin: Double = 10,
        targetMax: Double = 20,
        maxValue: Double = 30
    ) {
        self.progress = progress
        self.value = value
        self.targetMin = targetMin
        self.targetMax = targetMax
        self.maxValue = maxValue
    }
    
    var body: some View {
        ZStack {
            // Background with tick marks
            ForEach(0..<Int(maxValue), id: \.self) { tick in
                Rectangle()
                    .fill(tickColor(for: Double(tick)))
                    .frame(width: 2, height: tickHeight(for: Double(tick)))
                    .offset(y: -90)
                    .rotationEffect(.degrees(Double(tick) * 12)) // 360/30 = 12 degrees per tick
            }
            
            // Target range indicator
            Circle()
                .trim(
                    from: targetMin / maxValue,
                    to: targetMax / maxValue
                )
                .stroke(
                    Color.green.opacity(0.3),
                    style: StrokeStyle(lineWidth: 20, lineCap: .round)
                )
                .frame(width: 180, height: 180)
                .rotationEffect(.degrees(-90))
            
            // Current progress
            Circle()
                .trim(from: 0, to: animatedProgress)
                .stroke(
                    progressGradient,
                    style: StrokeStyle(lineWidth: 8, lineCap: .round)
                )
                .frame(width: 180, height: 180)
                .rotationEffect(.degrees(-90))
                .animation(.easeInOut(duration: 1.0), value: animatedProgress)
            
            // Center display
            VStack(spacing: 4) {
                Text("\(Int(animatedValue))")
                    .font(.system(size: 42, weight: .bold, design: .rounded))
                    .foregroundColor(.primary)
                    .contentTransition(.numericText())

                Text("cm H₂O")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
                    .fontWeight(.medium)

                // Status indicator
                Circle()
                    .fill(statusColor)
                    .frame(width: 8, height: 8)
                    .scaleEffect(isInTargetRange ? 1.2 : 0.8)
                    .animation(.easeInOut(duration: 0.5).repeatForever(autoreverses: true), value: isInTargetRange)
            }
        }
        .frame(width: 220, height: 220)
        .onAppear {
            withAnimation(.easeInOut(duration: 1.0)) {
                animatedProgress = progress
                animatedValue = value
            }
        }
        .onChange(of: progress) {
            withAnimation(.easeInOut(duration: 0.3)) {
                animatedProgress = progress
            }
        }
        .onChange(of: value) {
            withAnimation(.easeInOut(duration: 0.3)) {
                animatedValue = value
            }
        }
    }
    
    private func tickColor(for value: Double) -> Color {
        if value >= targetMin && value <= targetMax {
            return .green
        } else if value < targetMin {
            return .orange
        } else {
            return .red
        }
    }
    
    private func tickHeight(for value: Double) -> CGFloat {
        if value.truncatingRemainder(dividingBy: 5) == 0 {
            return 12 // Major tick
        } else {
            return 6  // Minor tick
        }
    }
    
    private var progressGradient: LinearGradient {
        if isInTargetRange {
            return LinearGradient(
                colors: [.green, .mint],
                startPoint: .leading,
                endPoint: .trailing
            )
        } else if value < targetMin {
            return LinearGradient(
                colors: [.orange, .yellow],
                startPoint: .leading,
                endPoint: .trailing
            )
        } else {
            return LinearGradient(
                colors: [.red, .orange],
                startPoint: .leading,
                endPoint: .trailing
            )
        }
    }
    
    private var statusColor: Color {
        if isInTargetRange {
            return .green
        } else if value < targetMin {
            return .orange
        } else {
            return .red
        }
    }
    
    private var isInTargetRange: Bool {
        value >= targetMin && value <= targetMax
    }
}

// MARK: - Breath-Focused Circular Progress View

struct BreathCircularProgressView: View {
    @ObservedObject var breathDetector: BreathDetector
    let zone: ExhalationZone
    let isBreathing: Bool

    @State private var animatedScale: Double = 1.0

    init(
        breathDetector: BreathDetector,
        zone: ExhalationZone,
        isBreathing: Bool
    ) {
        self.breathDetector = breathDetector
        self.zone = zone
        self.isBreathing = isBreathing
    }

    var body: some View {
        ZStack {
            // Background circle
            Circle()
                .stroke(Color.gray.opacity(0.2), lineWidth: 20)
                .frame(width: 180, height: 180)

            // Zone-based animated fill
            Circle()
                .fill(zone.color.opacity(0.3))
                .frame(width: 160, height: 160)
                .scaleEffect(isBreathing ? animatedScale : 0.8)
                .animation(.easeInOut(duration: 0.5), value: isBreathing)
                .animation(.easeInOut(duration: 0.3), value: zone)

            // Zone indicator ring
            Circle()
                .stroke(
                    zone.color,
                    style: StrokeStyle(lineWidth: 8, lineCap: .round)
                )
                .frame(width: 180, height: 180)
                .animation(.easeInOut(duration: 0.3), value: zone)

            // Center display - Zone info and breath count
            VStack(spacing: 8) {
                // Zone icon
                Image(systemName: zone.icon)
                    .font(.system(size: 48, weight: .bold))
                    .foregroundColor(zone.color)
                    .scaleEffect(isBreathing ? 1.1 : 1.0)
                    .animation(.easeInOut(duration: 0.5), value: isBreathing)

                // Zone description
                Text(zone.description)
                    .font(.title3)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)

                // Breath count (smaller, secondary display)
                HStack(spacing: 4) {
                    Text("\(breathDetector.currentBreathNumber)")
                        .font(.system(size: 20, weight: .bold, design: .rounded))
                        .foregroundColor(.white.opacity(0.8))
                        .contentTransition(.numericText())

                    Text("breaths")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.6))
                }

                // Current breath timer (if breathing)
                if breathDetector.isBreathing && breathDetector.currentBreathDuration > 0 {
                    Text(formatBreathDuration(breathDetector.currentBreathDuration))
                        .font(.system(size: 14, weight: .semibold, design: .monospaced))
                        .foregroundColor(.cyan)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(
                            Capsule()
                                .fill(Color.cyan.opacity(0.1))
                        )
                }
            }
        }
        .frame(width: 220, height: 220)
        .onAppear {
            withAnimation(.easeInOut(duration: 1.0)) {
                animatedScale = 1.0
            }
        }
        .onChange(of: isBreathing) { _, newValue in
            withAnimation(.easeInOut(duration: 0.5)) {
                animatedScale = newValue ? 1.1 : 1.0
            }
        }
    }



    private func formatBreathDuration(_ duration: TimeInterval) -> String {
        return String(format: "%.1fs", duration)
    }
}

#Preview {
    VStack(spacing: 40) {
        CircularProgressView(
            progress: 0.73,
            value: 73,
            maxValue: 100
        )

        DetailedCircularProgressView(
            progress: 0.6,
            value: 18,
            targetMin: 10,
            targetMax: 20,
            maxValue: 30
        )

        BreathCircularProgressView(
            breathDetector: BreathDetector(),
            zone: .goodTechnique,
            isBreathing: true
        )
    }
    .padding()
}
