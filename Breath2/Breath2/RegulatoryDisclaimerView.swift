//
//  RegulatoryDisclaimerView.swift
//  Breath2
//
//  Created by Mo on 02/06/2025.
//
//  Regulatory compliance disclaimer for wellness app positioning
//

import SwiftUI

struct RegulatoryDisclaimerView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 24) {
                    // Header
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Important Disclaimer")
                            .font(.title)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                        
                        Text("Please read this information carefully")
                            .font(.subheadline)
                            .foregroundColor(.white.opacity(0.7))
                    }
                    
                    // Main disclaimer content
                    VStack(alignment: .leading, spacing: 20) {
                        disclaimerSection(
                            title: "App Purpose",
                            content: "This application is designed to support your prescribed Acapella® device routine. It provides qualitative feedback on your exhalation technique based on the sound of the device to help you maintain a consistent effort."
                        )
                        
                        disclaimerSection(
                            title: "Not a Medical Device",
                            content: "This app does not measure lung pressure or any other physiological parameter. It is not a medical device and does not provide medical advice, diagnosis, or treatment."
                        )
                        
                        disclaimerSection(
                            title: "Healthcare Provider Consultation",
                            content: "Always consult your healthcare provider for any questions regarding your medical condition and before making any changes to your therapy. The data presented is for informational purposes to help you track your routine adherence."
                        )
                        
                        disclaimerSection(
                            title: "Technique Guidance Only",
                            content: "The app analyzes device sound patterns to provide qualitative zones (Good Technique, Low Effort, High Effort) for technique consistency. These zones are based on audio characteristics, not physiological measurements."
                        )
                        
                        disclaimerSection(
                            title: "Data Usage",
                            content: "Session data tracks time spent in different technique zones and breath durations to help you understand your routine consistency. This information is stored locally on your device."
                        )
                    }
                    
                    // Contact information
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Questions or Concerns?")
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.white)
                        
                        Text("If you have any questions about this app or your Acapella® device routine, please contact your healthcare provider.")
                            .font(.body)
                            .foregroundColor(.white.opacity(0.8))
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.blue.opacity(0.1))
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(Color.blue.opacity(0.3), lineWidth: 1)
                            )
                    )
                    
                    Spacer(minLength: 50)
                }
                .padding(20)
            }
            .background(
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color(red: 0.05, green: 0.05, blue: 0.08),
                        Color(red: 0.1, green: 0.1, blue: 0.15)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
            )
            .navigationTitle("About & Disclaimers")
            .navigationBarTitleDisplayMode(.inline)
            .toolbarBackground(Color(red: 0.05, green: 0.05, blue: 0.08), for: .navigationBar)
            .toolbarColorScheme(.dark, for: .navigationBar)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                    .foregroundColor(.blue)
                }
            }
        }
    }
    
    private func disclaimerSection(title: String, content: String) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.white)
            
            Text(content)
                .font(.body)
                .foregroundColor(.white.opacity(0.8))
                .lineSpacing(2)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.white.opacity(0.1), lineWidth: 1)
                )
        )
    }
}

#Preview {
    RegulatoryDisclaimerView()
}
