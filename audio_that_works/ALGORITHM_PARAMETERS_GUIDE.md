# Acapella Pitch Detection Algorithm - Parameters Guide

## Overview
This document provides comprehensive details about all configurable parameters in the Acapella pitch detection algorithm implementation, their effects on performance metrics, and recommended tuning ranges for different use cases.

## Table of Contents
- [Audio Input Parameters](#audio-input-parameters)
- [Frequency Detection Range](#frequency-detection-range)
- [Signal Processing Parameters](#signal-processing-parameters)
- [Autocorrelation Settings](#autocorrelation-settings)
- [Moving Average Parameters](#moving-average-parameters)
- [Buffer Management](#buffer-management)
- [Pressure Model Settings](#pressure-model-settings)
- [Performance Impact Matrix](#performance-impact-matrix)
- [Tuning Scenarios](#tuning-scenarios)
- [Parameter Validation](#parameter-validation)

---

## Audio Input Parameters

### Sample Rate (`sampleRate`)
**Current Value:** `44,100 Hz` (dynamic based on device)  
**Paper Specification:** `44,100 Hz` or `48,000 Hz`  
**Location:** `AudioManager.swift` line 25, `PitchDetector.swift` line 15

#### Description
The audio sampling frequency that determines the temporal resolution of the input signal.

#### Effects
- **📊 Accuracy**: Higher sample rates provide better frequency resolution
- **⚡ Performance**: Higher rates increase computational load linearly
- **🔄 UI Update**: No direct impact on update frequency
- **⏱️ Latency**: Minimal impact on processing latency
- **🌊 Smoothness**: Higher rates can provide smoother signal representation

#### Possible Values
| Value | Use Case | Trade-offs |
|-------|----------|------------|
| `22,050 Hz` | Low-power devices | Reduced accuracy, better battery life |
| `44,100 Hz` | **Standard (Current)** | Balanced performance and accuracy |
| `48,000 Hz` | High-accuracy mode | Better precision, higher CPU usage |
| `96,000 Hz` | Research/debugging | Excellent precision, significant CPU load |

#### Tuning Guidelines
- **Minimum Recommended:** 22,050 Hz (Nyquist frequency for 40 Hz detection)
- **Optimal Range:** 44,100 - 48,000 Hz
- **Maximum Practical:** 96,000 Hz (diminishing returns beyond this)

---

### Buffer Size (`bufferSize`)
**Current Value:** `0.1 seconds` (4,410 samples at 44.1kHz)  
**Paper Specification:** `0.1 seconds` recommended  
**Location:** `AudioManager.swift` line 26, calculated at line 133

#### Description
The size of audio chunks processed at each iteration, directly controlling the update frequency.

#### Effects
- **🔄 UI Update**: Smaller buffers = higher update frequency
- **⏱️ Latency**: Buffer size directly adds to system latency
- **⚡ Performance**: Smaller buffers = more frequent processing overhead
- **🎯 Reliability**: Larger buffers provide more data for stable detection
- **📊 Accuracy**: Larger buffers improve low-frequency detection

#### Possible Values
| Value | Update Rate | Latency | Use Case |
|-------|-------------|---------|----------|
| `0.05 sec` | 20 Hz | ~50ms | Real-time feedback |
| `0.1 sec` | **10 Hz (Current)** | ~100ms | **Balanced performance** |
| `0.2 sec` | 5 Hz | ~200ms | High accuracy mode |
| `0.5 sec` | 2 Hz | ~500ms | Research/analysis |

#### Tuning Guidelines
- **Minimum Recommended:** 0.05 seconds (for real-time feel)
- **Optimal Range:** 0.1 - 0.2 seconds
- **Maximum Practical:** 0.5 seconds (user experience degrades beyond this)

---

## Frequency Detection Range

### Minimum Frequency (`minFreq`)
**Current Value:** `7 Hz`  
**Paper Specification:** `10 Hz`  
**Location:** `AudioManager.swift` line 92, `PitchDetector.swift` line 16

#### Description
The lowest frequency the algorithm will attempt to detect.

#### Effects
- **🎯 Reliability**: Lower values increase false positive risk
- **📊 Accuracy**: Extended range allows detection of lower therapeutic pressures
- **⚡ Performance**: Lower frequencies require longer analysis windows
- **🔄 UI Update**: May affect detection frequency at low pressures

#### Possible Values
| Value | Pressure Range | Reliability | Performance |
|-------|----------------|-------------|-------------|
| `5 Hz` | Extended low | Lower | Slower |
| `7 Hz` | **Current** | **Good** | **Good** |
| `10 Hz` | Paper spec | Higher | Faster |
| `15 Hz` | Conservative | Highest | Fastest |

#### Tuning Guidelines
- **Therapeutic Minimum:** 7 Hz (allows detection of very low pressures)
- **Paper Specification:** 10 Hz (validated range)
- **Conservative Setting:** 15 Hz (highest reliability)

---

### Maximum Frequency (`maxFreq`)
**Current Value:** `40 Hz`  
**Paper Specification:** `40 Hz`  
**Location:** `AudioManager.swift` line 93, `PitchDetector.swift` line 17

#### Description
The highest frequency the algorithm will attempt to detect.

#### Effects
- **🎯 Reliability**: Higher values increase noise sensitivity
- **📊 Accuracy**: Must match therapeutic pressure range
- **⚡ Performance**: Higher frequencies require finer time resolution
- **🌊 Smoothness**: Affects Gaussian filter characteristics

#### Possible Values
| Value | Pressure Range | Noise Sensitivity | Use Case |
|-------|----------------|-------------------|----------|
| `30 Hz` | Conservative | Low | Noisy environments |
| `40 Hz` | **Paper spec (Current)** | **Medium** | **Standard therapy** |
| `50 Hz` | Extended | High | Research applications |
| `60 Hz` | Wide range | Very high | Development/testing |

#### Tuning Guidelines
- **Recommended:** 40 Hz (paper specification)
- **Conservative:** 30 Hz (for noisy environments)
- **Extended:** 50 Hz (research only)

---

## Signal Processing Parameters

### Downsample Factor (`downsampleFactor`)
**Current Value:** `45` (44.1kHz → 980Hz)  
**Paper Specification:** `45`  
**Location:** `PitchDetector.swift` line 24, calculated at line 75

#### Description
Factor by which the audio signal is downsampled to reduce computational load while preserving pitch information.

#### Effects
- **⚡ Performance**: Higher factors dramatically improve processing speed
- **📊 Accuracy**: Too high causes aliasing and loss of frequency resolution
- **🔄 UI Update**: Affects processing speed per chunk
- **🎯 Reliability**: Optimal value balances speed and precision

#### Possible Values
| Factor | Target Rate | Performance | Accuracy | Use Case |
|--------|-------------|-------------|----------|----------|
| `30` | 1,470 Hz | Good | Excellent | High-accuracy mode |
| `45` | **980 Hz (Current)** | **Excellent** | **Good** | **Standard operation** |
| `60` | 735 Hz | Excellent | Fair | Low-power devices |
| `90` | 490 Hz | Outstanding | Poor | Emergency/fallback |

#### Tuning Guidelines
- **High Accuracy:** 30-35 (better frequency resolution)
- **Balanced:** 40-50 (paper recommendation)
- **High Performance:** 55-70 (faster processing)

---

### Smoothing Filter Size (`smoothingFilterSize`)
**Current Value:** `176 samples` (sampleRate/250)  
**Paper Specification:** `176 samples` for 44.1kHz  
**Location:** `PitchDetector.swift` line 26, calculated at line 81

#### Description
Size of the moving average filter used to create the smoothed version that's subtracted from the raw signal.

#### Effects
- **🌊 Smoothness**: Larger filters provide smoother baseline removal
- **📊 Accuracy**: Too large can remove actual pitch information
- **⏱️ Latency**: Larger filters add processing delay
- **🎯 Reliability**: Optimal size removes noise while preserving signal

#### Possible Values
| Size | Smoothing | Latency | Accuracy | Use Case |
|------|-----------|---------|----------|----------|
| `100` | Light | Low | Good | Fast response |
| `176` | **Standard (Current)** | **Medium** | **Excellent** | **Balanced** |
| `250` | Heavy | High | Good | Noisy environments |
| `350` | Very heavy | Very high | Fair | Extreme noise |

#### Tuning Guidelines
- **Fast Response:** 100-150 samples
- **Standard:** 150-200 samples (paper range)
- **Noise Rejection:** 200-300 samples

---

## Autocorrelation Settings

### Correlation Threshold (`correlationThreshold`)
**Current Value:** `0.6`  
**Paper Specification:** `0.6`  
**Location:** `PitchDetector.swift` line 349

#### Description
Minimum correlation value required to accept a pitch detection as valid.

#### Effects
- **🎯 Reliability**: Higher thresholds reduce false positives
- **📊 Accuracy**: Lower thresholds increase detection rate but reduce precision
- **🔄 UI Update**: Affects frequency of successful detections
- **🌊 Smoothness**: Higher thresholds can cause intermittent readings

#### Possible Values
| Threshold | Detection Rate | Reliability | Use Case |
|-----------|----------------|-------------|----------|
| `0.4` | Very high | Low | Continuous feedback |
| `0.5` | High | Medium | Responsive mode |
| `0.6` | **Medium (Current)** | **High** | **Standard therapy** |
| `0.7` | Low | Very high | Precision mode |
| `0.8` | Very low | Excellent | Research/validation |

#### Tuning Guidelines
- **Responsive Mode:** 0.4-0.5 (more detections)
- **Balanced:** 0.5-0.6 (paper specification)
- **Precision Mode:** 0.7-0.8 (highest reliability)

---

### Coarse Search Step (`coarseStep`)
**Current Value:** `3 samples`  
**Paper Specification:** "rough resolution"  
**Location:** `PitchDetector.swift` line 301

#### Description
Step size for the initial coarse autocorrelation search phase.

#### Effects
- **⚡ Performance**: Larger steps significantly improve search speed
- **📊 Accuracy**: Larger steps may miss optimal correlation peaks
- **⏱️ Latency**: Affects initial detection speed
- **🎯 Reliability**: Balanced with fine search for accuracy

#### Possible Values
| Step Size | Speed Gain | Accuracy | Use Case |
|-----------|------------|----------|----------|
| `1` | None | Excellent | High-precision mode |
| `2` | 2x | Very good | Balanced accuracy |
| `3` | **3x (Current)** | **Good** | **Standard operation** |
| `4` | 4x | Fair | Performance mode |
| `5` | 5x | Poor | Emergency fallback |

#### Tuning Guidelines
- **High Accuracy:** 1-2 samples
- **Balanced:** 2-3 samples (current setting)
- **High Performance:** 4-5 samples

---

### Fine Search Window (`fineSearchWindow`)
**Current Value:** `±5 samples`  
**Paper Specification:** "finer resolution"  
**Location:** `PitchDetector.swift` line 320

#### Description
Range around the coarse maximum where fine-resolution search is performed.

#### Effects
- **📊 Accuracy**: Larger windows provide more thorough refinement
- **⚡ Performance**: Larger windows increase computation time
- **🎯 Reliability**: Balances thoroughness with efficiency
- **🔄 UI Update**: Affects processing time per detection

#### Possible Values
| Window Size | Accuracy | Performance | Use Case |
|-------------|----------|-------------|----------|
| `±3` | Good | Excellent | Fast mode |
| `±5` | **Very good (Current)** | **Good** | **Standard** |
| `±7` | Excellent | Fair | Precision mode |
| `±10` | Outstanding | Poor | Research mode |

#### Tuning Guidelines
- **Fast Mode:** ±3 samples
- **Standard:** ±5 samples (current)
- **Precision:** ±7-10 samples

---

## Moving Average Parameters

### Decay Rate (`decayRate`)
**Current Value:** `0.8`  
**Paper Specification:** `0.8`  
**Location:** `AudioManager.swift` line 96, `PitchDetector.swift` line 20

#### Description
Controls how quickly the moving averages adapt to new values. Higher values create more stable, slower-changing averages.

#### Effects
- **🌊 Smoothness**: Higher values create smoother output
- **⏱️ Responsiveness**: Lower values provide faster adaptation
- **🎯 Reliability**: Higher values reduce noise but may miss rapid changes
- **🔄 UI Update**: Affects how quickly displayed values change

#### Possible Values
| Decay Rate | Smoothness | Responsiveness | Use Case |
|------------|------------|----------------|----------|
| `0.6` | Low | High | Real-time feedback |
| `0.7` | Medium | Good | Responsive therapy |
| `0.8` | **High (Current)** | **Medium** | **Standard therapy** |
| `0.9` | Very high | Low | Stable readings |
| `0.95` | Extreme | Very low | Research/analysis |

#### Tuning Guidelines
- **Responsive:** 0.6-0.7 (faster adaptation)
- **Balanced:** 0.7-0.8 (paper specification)
- **Stable:** 0.85-0.95 (smoother output)

---

### Maximum Run Length (`maxRunLength`)
**Current Value:** `5`  
**Paper Specification:** `5` (calculated from decay rate)  
**Location:** `PitchDetector.swift` line 33

#### Description
Maximum number of consecutive successful detections before the moving average becomes fully weighted toward historical data.

#### Effects
- **🌊 Smoothness**: Higher values create more stable readings
- **⏱️ Responsiveness**: Lower values allow faster adaptation to changes
- **🎯 Reliability**: Prevents over-smoothing that could mask real changes
- **📊 Accuracy**: Balances stability with tracking ability

#### Possible Values
| Max Run Length | Stability | Adaptation Speed | Use Case |
|----------------|-----------|------------------|----------|
| `3` | Low | Fast | Dynamic feedback |
| `5` | **Medium (Current)** | **Balanced** | **Standard therapy** |
| `7` | High | Slow | Stable readings |
| `10` | Very high | Very slow | Research mode |

#### Tuning Guidelines
- **Dynamic:** 3-4 (faster adaptation)
- **Standard:** 4-6 (paper calculation)
- **Stable:** 7-10 (maximum smoothness)

---

### Leap Threshold (`leapThreshold`)
**Current Value:** `20%` of expected frequency  
**Paper Specification:** "leap exceeds expected amount"  
**Location:** `PitchDetector.swift` line 471

#### Description
Maximum allowed change in detected frequency before the run length is reduced, preventing sudden jumps in readings.

#### Effects
- **🎯 Reliability**: Lower thresholds catch smaller inconsistencies
- **🌊 Smoothness**: Higher thresholds allow more natural variation
- **📊 Accuracy**: Balances noise rejection with legitimate signal changes
- **⏱️ Responsiveness**: Affects how quickly the system adapts to real changes

#### Possible Values
| Threshold | Sensitivity | Smoothness | Use Case |
|-----------|-------------|------------|----------|
| `10%` | Very high | Low | Noise rejection |
| `15%` | High | Medium | Sensitive detection |
| `20%` | **Medium (Current)** | **High** | **Balanced** |
| `25%` | Low | Very high | Stable readings |
| `30%` | Very low | Extreme | Research mode |

#### Tuning Guidelines
- **Sensitive:** 10-15% (catches small inconsistencies)
- **Balanced:** 15-25% (current range)
- **Permissive:** 25-35% (allows more variation)

---

## Buffer Management

### Target Buffer Length (`targetBufferLength`)
**Current Value:** `300 samples` (~0.3 seconds at 980Hz)  
**Paper Specification:** Not specified  
**Location:** `PitchDetector.swift` line 49

#### Description
Amount of processed audio data accumulated before performing pitch detection.

#### Effects
- **⏱️ Latency**: Larger buffers increase detection delay
- **📊 Accuracy**: Larger buffers provide more data for correlation analysis
- **⚡ Performance**: Larger buffers use more memory but may be more efficient
- **🎯 Reliability**: More data generally improves detection reliability

#### Possible Values
| Buffer Length | Latency | Accuracy | Memory | Use Case |
|---------------|---------|----------|---------|----------|
| `150` | ~0.15s | Good | Low | Fast response |
| `300` | **~0.3s (Current)** | **Very good** | **Medium** | **Standard** |
| `500` | ~0.5s | Excellent | High | High accuracy |
| `1000` | ~1.0s | Outstanding | Very high | Research |

#### Tuning Guidelines
- **Fast Response:** 150-250 samples
- **Balanced:** 250-400 samples (current range)
- **High Accuracy:** 400-600 samples

---

### Minimum Data Check (`minDataCheck`)
**Current Value:** `100 samples`  
**Paper Specification:** Not specified  
**Location:** `PitchDetector.swift` line 126

#### Description
Minimum amount of audio data required before attempting pitch detection.

#### Effects
- **🎯 Reliability**: Higher values prevent spurious detections from insufficient data
- **⏱️ Latency**: Higher values increase startup time
- **📊 Accuracy**: Ensures sufficient data for meaningful correlation
- **🔄 UI Update**: Affects how quickly detection begins

#### Possible Values
| Min Data | Startup Time | Reliability | Use Case |
|----------|--------------|-------------|----------|
| `50` | Fast | Lower | Quick start |
| `100` | **Medium (Current)** | **Good** | **Balanced** |
| `200` | Slow | High | Reliable start |
| `500` | Very slow | Very high | Research mode |

#### Tuning Guidelines
- **Quick Start:** 50-75 samples
- **Balanced:** 75-150 samples
- **Reliable:** 150-300 samples

---

## Pressure Model Settings

### Linear Model Coefficients
**Current Values:** `slope = 1.119`, `intercept = -4.659`  
**Paper Specification:** From research data (r² = 0.886)  
**Location:** `PressureCalculator.swift` lines 35-36

#### Description
Linear relationship coefficients: `Pressure = slope × Frequency + intercept`

#### Effects
- **📊 Accuracy**: Based on validated research data
- **🔄 UI Update**: Affects pressure display scaling
- **🎯 Reliability**: Validated across 9,993 data points
- **🌊 Smoothness**: Linear relationship provides predictable scaling

#### Validation
- **R-squared:** 0.886 (strong correlation)
- **Data Points:** 9,993 measurements
- **Frequency Range:** 7-40 Hz
- **Pressure Range:** 6-30 cm H2O

**⚠️ Warning:** These coefficients should not be modified without extensive validation as they are based on clinical research data.

---

### Pressure Validation Ranges
**Current Values:**  
- `minValidFreq = 7.0 Hz`
- `maxValidFreq = 40.0 Hz`  
- `minValidPressure = 6.0 cm H2O`
- `maxValidPressure = 30.0 cm H2O`

**Location:** `PressureCalculator.swift` lines 24-27

#### Description
Validation ranges that filter out unrealistic frequency and pressure values.

#### Effects
- **🎯 Reliability**: Prevents display of impossible values
- **🔄 UI Update**: Affects what values are shown to users
- **📊 Accuracy**: Ensures readings are within therapeutic ranges
- **🌊 Smoothness**: Eliminates outlier spikes

#### Tuning Guidelines
- **Conservative:** Narrow ranges for high reliability
- **Standard:** Current ranges (validated)
- **Extended:** Wider ranges for research applications

---

## Performance Impact Matrix

| Parameter | UI Speed | Reliability | Accuracy | Smoothness | Performance | Latency |
|-----------|----------|-------------|----------|------------|-------------|---------|
| **Sample Rate** | ○ | ○ | ●●● | ●● | ●●● | ○ |
| **Buffer Size** | ●●● | ●● | ●● | ● | ●● | ●●● |
| **Min/Max Freq** | ● | ●●● | ●● | ○ | ●● | ○ |
| **Downsample Factor** | ●● | ● | ●●● | ● | ●●● | ● |
| **Smoothing Filter** | ○ | ●● | ●● | ●●● | ● | ●● |
| **Correlation Threshold** | ●●● | ●●● | ●● | ●● | ○ | ○ |
| **Search Parameters** | ● | ● | ●●● | ○ | ●●● | ● |
| **Decay Rate** | ●● | ●● | ● | ●●● | ○ | ●● |
| **Run Length** | ● | ●● | ● | ●●● | ○ | ● |
| **Buffer Length** | ● | ●● | ●●● | ● | ● | ●●● |

**Legend:** ○ = No impact, ● = Low impact, ●● = Medium impact, ●●● = High impact

---

## Tuning Scenarios

### Real-Time Feedback Mode
**Goal:** Minimize latency and maximize update frequency

```swift
// Recommended settings
bufferSize = 0.05 // 50ms buffers
correlationThreshold = 0.5 // Lower threshold for more detections
decayRate = 0.7 // Faster adaptation
targetBufferLength = 200 // Smaller accumulation buffer
coarseStep = 2 // Slightly more accurate search
```

**Trade-offs:** Reduced accuracy and reliability for faster response

### High Accuracy Mode
**Goal:** Maximum precision and reliability

```swift
// Recommended settings
bufferSize = 0.2 // Larger buffers for more data
correlationThreshold = 0.7 // Higher threshold for reliability
downsampleFactor = 35 // Less downsampling for better resolution
fineSearchWindow = 7 // More thorough fine search
targetBufferLength = 500 // More data for correlation
```

**Trade-offs:** Higher latency and CPU usage for better accuracy

### Stable Readings Mode
**Goal:** Smooth, stable output with minimal fluctuations

```swift
// Recommended settings
decayRate = 0.9 // Heavy smoothing
maxRunLength = 7 // Longer stability period
leapThreshold = 0.15 // Stricter change detection
smoothingFilterSize = 250 // More baseline smoothing
```

**Trade-offs:** Slower response to real changes for stability

### Low-Power Mode
**Goal:** Minimize CPU usage and battery consumption

```swift
// Recommended settings
sampleRate = 22050 // Lower sample rate
downsampleFactor = 60 // More aggressive downsampling
coarseStep = 4 // Faster coarse search
bufferSize = 0.15 // Less frequent processing
targetBufferLength = 250 // Smaller buffers
```

**Trade-offs:** Reduced accuracy and responsiveness for efficiency

### Noisy Environment Mode
**Goal:** Robust operation in high-noise conditions

```swift
// Recommended settings
correlationThreshold = 0.7 // Higher threshold for noise rejection
smoothingFilterSize = 300 // More aggressive noise filtering
minFreq = 10 // Conservative frequency range
maxFreq = 35 // Avoid high-frequency noise
leapThreshold = 0.15 // Stricter consistency checking
```

**Trade-offs:** May miss some valid detections for noise immunity

---

## Parameter Validation

### Critical Constraints
1. **Sample Rate:** Must be ≥ 2 × maxFreq (Nyquist criterion)
2. **Buffer Size:** Must be > 0 and ≤ 1.0 second
3. **Frequency Range:** minFreq < maxFreq, both > 0
4. **Correlation Threshold:** 0.0 ≤ threshold ≤ 1.0
5. **Decay Rate:** 0.0 < rate < 1.0
6. **Downsample Factor:** Must result in rate > 2 × maxFreq

### Recommended Validation Code
```swift
func validateParameters() -> Bool {
    guard sampleRate >= 2 * Float(maxFreq) else { return false }
    guard bufferSize > 0 && bufferSize <= 1.0 else { return false }
    guard minFreq > 0 && minFreq < maxFreq else { return false }
    guard correlationThreshold >= 0.0 && correlationThreshold <= 1.0 else { return false }
    guard decayRate > 0.0 && decayRate < 1.0 else { return false }
    guard downsampledRate >= 2 * Float(maxFreq) else { return false }
    return true
}
```

### Performance Monitoring
Monitor these metrics to ensure parameter changes don't degrade performance:
- **Processing Time:** Should be < 10% of buffer duration
- **Memory Usage:** Should remain < 50MB for mobile devices
- **Detection Rate:** Should maintain > 80% successful detections
- **Latency:** Total system latency should be < 300ms

---

## Implementation Notes

### Parameter Persistence
Consider storing user-customized parameters in `UserDefaults`:
```swift
UserDefaults.standard.set(correlationThreshold, forKey: "correlationThreshold")
UserDefaults.standard.set(decayRate, forKey: "decayRate")
```

### Runtime Adjustment
Some parameters can be safely adjusted during runtime:
- Correlation threshold
- Decay rate
- Leap threshold
- Buffer management settings

Others require reinitialization:
- Sample rate
- Frequency ranges
- Downsample factor
- Filter sizes

### Testing Recommendations
When modifying parameters:
1. Test with synthetic sine waves at known frequencies
2. Validate with real breath sounds
3. Measure performance impact
4. Verify therapeutic pressure ranges remain accurate
5. Test edge cases (very low/high pressures)

---

---

## Advanced Configuration Examples

### Custom Parameter Sets for Different Use Cases

#### Pediatric Therapy (Lower Pressures)
```swift
// Optimized for children with lower lung capacity
let pediatricConfig = PitchDetectorConfig(
    minFreq: 5,              // Extended low range
    maxFreq: 30,             // Reduced high range
    correlationThreshold: 0.55, // Slightly lower for sensitivity
    decayRate: 0.75,         // Faster adaptation
    bufferSize: 0.08         // Quicker response
)
```

#### Elderly/COPD Patients (Stability Focus)
```swift
// Optimized for patients with breathing difficulties
let elderlyConfig = PitchDetectorConfig(
    minFreq: 8,              // Conservative low range
    maxFreq: 35,             // Conservative high range
    correlationThreshold: 0.65, // Higher reliability
    decayRate: 0.85,         // More stability
    maxRunLength: 7,         // Extended smoothing
    leapThreshold: 0.12      // Stricter consistency
)
```

#### Athletic/High-Performance (Precision Mode)
```swift
// Optimized for athletes with strong lung capacity
let athleticConfig = PitchDetectorConfig(
    minFreq: 10,             // Standard low range
    maxFreq: 45,             // Extended high range
    correlationThreshold: 0.7, // High precision
    decayRate: 0.75,         // Responsive adaptation
    downsampleFactor: 35,    // Better resolution
    fineSearchWindow: 8      // Thorough search
)
```

#### Research/Clinical Study Mode
```swift
// Optimized for data collection and analysis
let researchConfig = PitchDetectorConfig(
    sampleRate: 48000,       // Higher sample rate
    bufferSize: 0.25,        // Larger analysis windows
    correlationThreshold: 0.75, // High confidence
    saveResults: true,       // Enable data logging
    targetBufferLength: 600, // Extended analysis
    downsampleFactor: 40     // Balanced resolution
)
```

---

## Troubleshooting Common Issues

### Issue: Intermittent Detection
**Symptoms:** Readings appear and disappear frequently
**Likely Causes:**
- Correlation threshold too high
- Insufficient buffer length
- Noisy environment

**Solutions:**
```swift
// Reduce correlation threshold
correlationThreshold = 0.5  // From 0.6

// Increase buffer length
targetBufferLength = 400    // From 300

// Improve noise filtering
smoothingFilterSize = 220   // From 176
```

### Issue: Slow Response
**Symptoms:** Readings lag behind actual pressure changes
**Likely Causes:**
- High decay rate
- Large buffer sizes
- Excessive smoothing

**Solutions:**
```swift
// Reduce decay rate
decayRate = 0.7            // From 0.8

// Smaller buffers
bufferSize = 0.08          // From 0.1
targetBufferLength = 250   // From 300

// Reduce smoothing
smoothingFilterSize = 150  // From 176
```

### Issue: Unstable Readings
**Symptoms:** Values jump around rapidly
**Likely Causes:**
- Low correlation threshold
- Insufficient smoothing
- High leap threshold

**Solutions:**
```swift
// Increase correlation threshold
correlationThreshold = 0.65 // From 0.6

// Increase smoothing
decayRate = 0.85           // From 0.8
maxRunLength = 6           // From 5

// Stricter leap detection
leapThreshold = 0.15       // From 0.20
```

### Issue: Poor Low-Frequency Detection
**Symptoms:** Difficulty detecting pressures below 10 cm H2O
**Likely Causes:**
- Minimum frequency too high
- Insufficient buffer length
- Aggressive downsampling

**Solutions:**
```swift
// Lower minimum frequency
minFreq = 6                // From 7

// Increase analysis window
targetBufferLength = 450   // From 300

// Reduce downsampling
downsampleFactor = 40      // From 45
```

### Issue: High CPU Usage
**Symptoms:** Device heating, battery drain, performance issues
**Likely Causes:**
- High sample rate
- Large search windows
- Frequent processing

**Solutions:**
```swift
// Optimize for performance
downsampleFactor = 55      // From 45
coarseStep = 4             // From 3
bufferSize = 0.12          // From 0.1
fineSearchWindow = 4       // From 5
```

---

## Parameter Interaction Effects

### Frequency Range ↔ Performance
- **Wider ranges** require more autocorrelation computations
- **Lower minimum frequencies** need longer analysis windows
- **Higher maximum frequencies** require finer time resolution

### Buffer Size ↔ Accuracy Trade-off
- **Smaller buffers**: Faster updates, less data for analysis
- **Larger buffers**: More accurate detection, higher latency
- **Optimal range**: 0.08-0.15 seconds for most applications

### Smoothing ↔ Responsiveness Balance
- **Heavy smoothing**: Stable readings, slow adaptation
- **Light smoothing**: Fast response, more noise
- **Dynamic adjustment**: Consider adaptive smoothing based on signal quality

### Threshold ↔ Detection Rate Relationship
- **Lower thresholds**: More detections, higher false positive rate
- **Higher thresholds**: Fewer detections, higher reliability
- **Adaptive thresholds**: Adjust based on signal-to-noise ratio

---

## Performance Optimization Strategies

### Memory Management
```swift
// Pre-allocate buffers to avoid runtime allocation
private var correlationBuffer = [Float](repeating: 0.0, count: maxBufferSize)
private var tempBuffer = [Float](repeating: 0.0, count: maxBufferSize)

// Reuse buffers instead of creating new ones
func processChunk(_ audioData: [Float]) -> Float {
    // Reuse existing buffers instead of creating new arrays
    correlationBuffer.removeAll(keepingCapacity: true)
    // ... processing logic
}
```

### CPU Optimization
```swift
// Use SIMD operations for vector math
import simd

// Vectorized correlation calculation
func computeCorrelationSIMD(_ data: [Float], lag: Int) -> Float {
    let length = data.count - lag
    let vector1 = Array(data[0..<length])
    let vector2 = Array(data[lag..<data.count])

    // Use accelerated dot product
    var result: Float = 0.0
    vDSP_dotpr(vector1, 1, vector2, 1, &result, vDSP_Length(length))
    return result
}
```

### Adaptive Processing
```swift
// Adjust processing intensity based on device capabilities
func adaptToDeviceCapabilities() {
    let processorCount = ProcessInfo.processInfo.processorCount
    let availableMemory = ProcessInfo.processInfo.physicalMemory

    if processorCount < 4 || availableMemory < 2_000_000_000 {
        // Low-power device optimizations
        downsampleFactor = 60
        coarseStep = 4
        targetBufferLength = 200
    }
}
```

---

## Quality Assurance Guidelines

### Parameter Validation Checklist
- [ ] All frequency ranges are positive and ordered correctly
- [ ] Correlation thresholds are between 0.0 and 1.0
- [ ] Buffer sizes are reasonable (0.05-0.5 seconds)
- [ ] Decay rates are between 0.0 and 1.0
- [ ] Downsample factors maintain Nyquist criterion
- [ ] Search parameters are computationally feasible

### Testing Protocol
1. **Synthetic Signal Testing**
   - Generate pure tones at 10, 15, 20, 25, 30, 35, 40 Hz
   - Verify detection accuracy within ±2.5%
   - Test with added white noise at various SNR levels

2. **Real-World Testing**
   - Test with actual breath sounds from multiple users
   - Verify pressure readings match reference measurements
   - Test in various acoustic environments

3. **Performance Testing**
   - Measure processing time per buffer
   - Monitor memory usage over extended sessions
   - Verify real-time performance on target devices

4. **Edge Case Testing**
   - Very low pressures (< 8 cm H2O)
   - Very high pressures (> 25 cm H2O)
   - Rapid pressure changes
   - Intermittent breathing patterns

### Regression Testing
When modifying parameters, always verify:
- Detection accuracy hasn't degraded
- Performance remains within acceptable limits
- User experience quality is maintained
- Clinical pressure ranges remain valid

---

## Future Enhancement Opportunities

### Adaptive Parameter Adjustment
- **Signal Quality Assessment**: Automatically adjust thresholds based on noise levels
- **User-Specific Calibration**: Learn optimal parameters for individual users
- **Environmental Adaptation**: Adjust for different acoustic conditions

### Machine Learning Integration
- **Pattern Recognition**: Use ML to improve detection reliability
- **Predictive Adjustment**: Anticipate parameter needs based on usage patterns
- **Anomaly Detection**: Identify and filter unusual readings

### Advanced Signal Processing
- **Multi-Resolution Analysis**: Use wavelets for better time-frequency analysis
- **Adaptive Filtering**: Dynamic filter adjustment based on signal characteristics
- **Spectral Analysis**: Complement autocorrelation with frequency domain methods

---

*This comprehensive guide should be updated whenever algorithm parameters are modified, new tuning scenarios are discovered, or performance characteristics change. Regular review ensures optimal algorithm performance across all use cases.*

**Document Version:** 1.0
**Last Updated:** December 2024
**Next Review:** March 2025
