Regulatory Compliance Refactor Plan: Project Tracker
Objective: To refactor the application to align with a low-risk, non-medical device regulatory strategy (e.g., FDA Enforcement Discretion, EU General Wellness).

Core Mandate: The app must be transformed from a quantitative physiological monitor into a qualitative technique and adherence guide. It must not calculate, display, store, or act upon a specific physiological pressure value (cm H₂O). The app's "knowledge" must be abstracted to qualitative zones related to device sound.

Guiding Principles & Executive Summary
This refactor is guided by a few core principles. Every change should be checked against them.

Risk Area	Problematic Implementation	Compliant Solution
1. Core Calculation	PressureCalculator.swift calculates and returns a specific Float pressure value.	Refactor to TechniqueAnalyzer.swift. The primary method must return a qualitative enum (e.g., ExhalationZone), not a number. The internal pressure math becomes a hidden implementation detail.
2. User Feedback	The UI gives prescriptive commands ("Blow harder," "Reduce pressure") based on pressure numbers.	The UI must provide descriptive feedback about the device's sound ("Device flutter is low," "Device flutter is steady"). The app describes, it does not command.
3. Data Display & UI	Charts and UI elements show numerical pressure values with units like "cm H₂O".	All numerical pressure displays must be removed. UI will use colors, icons, and text to represent qualitative zones (e.g., "Good Technique"). High-risk charts must be removed or replaced.
4. Data Storage	Session history stores a timeline of pressure readings and calculates pressure-based metrics (averagePressure).	Session history must store the percentage of time spent in each qualitative zone. Metrics must reflect adherence and consistency, not physiological measurement.
5. Language & Lexicon	The app uses clinical or diagnostic terms ("Therapy," "Effort," "Session Quality").	All medical-sounding language must be scrubbed and replaced with wellness/adherence terms ("Routine," "Usage," "Session Consistency").
Phase 1: The Core Logic Refactor (The Highest Priority)
This phase removes the primary regulatory risk at its source.

Task 1.1: Rename and Refactor PressureCalculator.swift
Action: Rename the file Breath2/Breath2/PressureCalculator.swift to TechniqueAnalyzer.swift.
Action: Rename the class PressureCalculator to TechniqueAnalyzer.
Task 1.2: Create the Qualitative Zone Enum
Action: In TechniqueAnalyzer.swift, define a new enum to represent the qualitative zones. This will be the only output from the analyzer.
// File: TechniqueAnalyzer.swift

// NEW ENUM to define qualitative zones
enum ExhalationZone {
    case noEffort
    case lowEffort
    case goodTechnique
    case highEffort
}
Use code with caution.
Swift
Task 1.3: Modify the Calculation Method
Action: Refactor the calculatePressure(fromPitch:) method to classifyPitch(_:). It must return the new ExhalationZone enum, not a Float. The internal calculation is acceptable as an implementation detail, but its result must not be exposed.
BEFORE (Problematic):
// In PressureCalculator.swift
class PressureCalculator {
    func calculatePressure(fromPitch pitch: Float) -> Float {
        let calculatedPressure = slope * pitch + intercept
        return clampedPressure
    }
}
Use code with caution.
Swift
AFTER (Compliant):
// File: TechniqueAnalyzer.swift
class TechniqueAnalyzer {
    // Keep the model for internal logic, but it's now an implementation detail
    private let slope: Float = 1.119
    private let intercept: Float = -4.659

    // The public function now returns a zone, not a pressure value
    func classifyPitch(_ pitch: Float) -> ExhalationZone {
        // Validation of frequency is still good practice
        guard pitch >= 7.0 && pitch <= 40.0 else {
            return .noEffort
        }

        // The internal calculation remains, but its result is only used here
        let internalPressureValue = slope * pitch + intercept

        // Logic to map internal value to a qualitative zone
        if internalPressureValue < 10.0 {
            return .lowEffort
        } else if internalPressureValue <= 20.0 {
            return .goodTechnique
        } else {
            return .highEffort
        }
    }
}
Use code with caution.
Swift
Phase 2: Update Data Flow & State Management
Propagate the new ExhalationZone through the app instead of currentPressure.

Task 2.1: Update AudioManager.swift
Action: In AudioManager.swift, replace the pressureCalculator instance with techniqueAnalyzer.
Action: Replace the published property @Published var currentPressure: Double with @Published var currentZone: ExhalationZone = .noEffort.
Action: Update the audio processing logic to use the new analyzer and publish the new zone.
BEFORE:
// In AudioManager.swift
@Published var currentPressure: Double = 0.0
private var pressureCalculator: PressureCalculator?

private func processAudioBuffer(_ buffer: AVAudioPCMBuffer) {
    let calculatedPressure = pressureCalculator.calculatePressure(fromPitch: detectedPitch)
    self.currentPressure = Double(calculatedPressure)
}
Use code with caution.
Swift
AFTER:
// In AudioManager.swift
@Published var currentZone: ExhalationZone = .noEffort
private var techniqueAnalyzer: TechniqueAnalyzer?

// In init()
// self.pressureCalculator = PressureCalculator() // REMOVE
self.techniqueAnalyzer = TechniqueAnalyzer() // ADD

// In processAudioBuffer()
private func processAudioBuffer(_ buffer: AVAudioPCMBuffer) {
    guard let techniqueAnalyzer = techniqueAnalyzer else { return }
    // ... (pitch detection logic) ...
    let classifiedZone = techniqueAnalyzer.classifyPitch(detectedPitch)
    
    DispatchQueue.main.async {
        self.currentZone = classifiedZone
        // ... any other UI updates
    }
}
Use code with caution.
Swift
Phase 3: Overhaul Data Storage & History
Data logs must not contain physiological measurements. They should reflect adherence to a technique.

Task 3.1: Modify TherapySession.swift
Action: Remove the pressureReadings property.
Action: Add new properties to track the time spent in each zone.
Action: Create a new function updateZone(_:) to be called when the audioManager.currentZone changes.
BEFORE:
// In TherapySession.swift
@Published var pressureReadings: [PressureReading] = []
func updatePressure(_ pressure: Double) { /* ... */ }
Use code with caution.
Swift
AFTER:
// In TherapySession.swift
@Published var timeInGoodZone: TimeInterval = 0
@Published var timeInLowZone: TimeInterval = 0
@Published var timeInHighZone: TimeInterval = 0
private var lastZoneUpdate: Date?

func startSession() {
    // ... existing logic ...
    lastZoneUpdate = Date() // Initialize the timestamp
}

// New function to be called from the View
func updateZone(_ zone: ExhalationZone) {
    guard let lastUpdate = lastZoneUpdate, isActive else {
        lastZoneUpdate = Date()
        return
    }

    let timeDelta = Date().timeIntervalSince(lastUpdate)
    switch zone {
    case .lowEffort: timeInLowZone += timeDelta
    case .goodTechnique: timeInGoodZone += timeDelta
    case .highEffort: timeInHighZone += timeDelta
    case .noEffort: break
    }
    lastZoneUpdate = Date()
}
Use code with caution.
Swift
Task 3.2: Modify SessionHistoryManager.swift
Action: Redesign the CompletedSession struct to store zone percentages, not pressure data.
BEFORE:
struct CompletedSession: Codable, Identifiable {
    let averagePressure: Double
    let maxPressure: Double
    let pressureReadings: [StoredPressureReading]
    // ... other properties
}
Use code with caution.
Swift
AFTER:
struct CompletedSession: Codable, Identifiable {
    let id: UUID
    let date: Date
    let duration: TimeInterval
    let stepsCompleted: Int
    
    // NEW PROPERTIES
    let percentInGoodZone: Double
    let percentInLowZone: Double
    let percentInHighZone: Double
    
    // LOW-RISK BEHAVIORAL METRICS (CAN STAY)
    let averageExhalationDuration: Double 
    
    // REMOVED PROPERTIES
    // let averagePressure: Double
    // let maxPressure: Double
    // let pressureReadings: [StoredPressureReading]
    
    // Update the initializer to calculate and store these new values from a TherapySession object.
    init(from session: TherapySession) {
        // ...
        let totalZoneTime = session.timeInGoodZone + session.timeInLowZone + session.timeInHighZone
        if totalZoneTime > 0 {
            self.percentInGoodZone = (session.timeInGoodZone / totalZoneTime) * 100
            self.percentInLowZone = (session.timeInLowZone / totalZoneTime) * 100
            self.percentInHighZone = (session.timeInHighZone / totalZoneTime) * 100
        } else {
            self.percentInGoodZone = 0
            self.percentInLowZone = 0
            self.percentInHighZone = 0
        }
        // ...
    }
}
Use code with caution.
Swift
Phase 4: Overhaul User Interface & Experience (UI/UX)
The user must never see a number for pressure. All feedback must be qualitative and descriptive.

Task 4.1: Redesign the Main Progress View (CircularProgressView.swift)
Action: Remove any display of a numerical value (e.g., 15.4 cm H₂O).
Action: The view should now be driven by the ExhalationZone state.
Action: Use color, icons, and text to indicate the current zone. Remove any "progress" element that maps directly to a pressure number.
NEW DESIGN CONCEPT:
// In a new or refactored BreathCircularProgressView.swift
struct BreathCircularProgressView: View {
    let zone: ExhalationZone
    let isBreathing: Bool // To control animations

    var body: some View {
        ZStack {
            // Background
            Circle().stroke(Color.gray.opacity(0.2), lineWidth: 20)

            // Animated color fill based on zone
            Circle()
                .fill(zoneColor.opacity(0.3))
                .scaleEffect(isBreathing ? 1.0 : 0.0)
                .animation(.easeInOut(duration: 0.5), value: isBreathing)
            
            // Center display - NO NUMBERS, ONLY QUALITATIVE INFO
            VStack(spacing: 8) {
                Image(systemName: zoneIcon)
                    .font(.system(size: 48, weight: .bold))
                Text(zoneText)
                    .font(.title2).fontWeight(.semibold)
            }
            .foregroundColor(zoneColor)
        }
    }
    
    // Helper properties for dynamic UI
    private var zoneColor: Color { /* ... */ }
    private var zoneText: String { /* ... */ }
    private var zoneIcon: String { /* ... */ }
}
Use code with caution.
Swift
Task 4.2: Update TherapyView.swift
Action: Remove the realTimeMessageArea that shows prescriptive feedback like "Harder" or "Softer".
Action: Instead, display descriptive feedback about the device's state. This can be driven by a new property on TherapySession.
Action: Pass the audioManager.currentZone to the redesigned BreathCircularProgressView.
Action: Call therapySession.updateZone(newZone) in an .onChange modifier.
Task 4.3: Remove and Replace High-Risk Charts
Action: Delete PressureTimelineChart.swift and any associated views. This chart is fundamentally non-compliant.
Action: In SessionDetailView.swift, replace the pressure timeline chart with a new, safe chart that shows the percentage of time spent in each zone. A horizontal bar chart is a good, safe option.
Action: The ExhalationTimeChart is low-risk and can remain, as it tracks a behavioral/adherence metric (breath duration), not a physiological one.
EXAMPLE OF A SAFE REPLACEMENT CHART:
// In a new file, e.g., ZonePerformanceChart.swift
import SwiftUI
import Charts

struct ZonePerformanceChart: View {
    let session: CompletedSession

    var body: some View {
        Chart {
            BarMark(
                x: .value("Percentage", session.percentInLowZone),
                y: .value("Zone", "Low Effort")
            ).foregroundStyle(.orange)
            
            BarMark(
                x: .value("Percentage", session.percentInGoodZone),
                y: .value("Zone", "Good Technique")
            ).foregroundStyle(.green)

            BarMark(
                x: .value("Percentage", session.percentInHighZone),
                y: .value("Zone", "High Effort")
            ).foregroundStyle(.red)
        }
        .chartXAxisLabel("Time in Zone (%)")
        .chartYAxis { AxisMarks(preset: .extended) }
        .frame(height: 150)
    }
}
Use code with caution.
Swift
Phase 5: Lexicon and Content Scrub
Language is critical for defining the app's intended use.

Task 5.1: System-Wide Text Replacement
Action: Perform a project-wide search and replace for the following terms in all user-facing strings, comments, and variable names where appropriate.
Replace This (Risky)	With This (Low-Risk & Compliant)
Pressure (cm H₂O)	Technique Zone, Effort Zone, Flutter Sound
Therapy / Treatment	Session / Routine
Measure / Monitor	Guide / Provide Feedback
Session Quality	Session Consistency / Adherence
Effort	Usage / Technique
Diagnose / Optimize	Help / Support
Task 5.2: Update Specific UI Text
TabBarView.swift: Change "PEP Therapy" tab to "PEP Routine" or "My Session".
TherapyView.swift Header: Change "PEP Therapy" to "My PEP Routine".
SessionDetailView.swift: Rename "Effort" tab to "Technique" or "Usage". Rename "Session Quality" card to "Session Consistency".
Task 5.3: Add Prominent Disclaimer
Action: Create a new "About & Disclaimers" section in the app's settings.
Action: Add the following text. It must be easily accessible to the user.
Disclaimer: This application is designed to support your prescribed Acapella® device routine. It provides qualitative feedback on your exhalation technique based on the sound of the device to help you maintain a consistent effort. This app does not measure lung pressure or any other physiological parameter. It is not a medical device and does not provide medical advice, diagnosis, or treatment. Always consult your healthcare provider for any questions regarding your medical condition and before making any changes to your therapy. The data presented is for informational purposes to help you track your routine adherence.
Phase 6: Project Hygiene and Cleanup
Internal documentation can be reviewed and should align with the app's public positioning.

Task 6.1: Archive High-Risk Internal Files
Action: Locate files that explicitly state the intent to "predict pressure" or match clinical papers. These files should be archived outside of the project repository.
Pitch_Papaer/textAcapellaPitchDetectionV3.txt
PAPER_COMPLIANCE_FIXES.md
Task 6.2: Scrub Internal Documentation
Action: Review ALGORITHM_PARAMETERS_GUIDE.md and any other internal developer notes.
Action: Remove all references to "Pressure" and rephrase sections to be about "classifying pitch into effort zones" to maintain consistency with the new app architecture.
Final Review Checklist
Have all references to PressureCalculator been removed and replaced with TechniqueAnalyzer?
Does the app publish and operate on ExhalationZone instead of a numerical currentPressure?
Has the PressureTimelineChart been completely removed from the codebase and UI?
Is all user-facing feedback descriptive (describing the sound) rather than prescriptive (telling the user what to do)?
Has the CompletedSession data model been updated to store zone percentages instead of pressure readings?
Has all user-facing text been scrubbed according to the lexicon table?
Is the disclaimer present and easily accessible in the app?
Have the high-risk internal documents been archived outside the repository?